# Required: OpenRouter API Key for AI responses
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Optional: Google Gemini API Key for enhanced embeddings
GEMINI_API_KEY=your_gemini_api_key_here
# Alternative environment variable name (both work)
NEXT_PUBLIC_GEMINI_API_KEY=your_gemini_api_key_here

# Required: Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Optional: Site URL (defaults to localhost:3000)
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# Optional: Default AI model for OpenRouter
OPENROUTER_DEFAULT_MODEL=deepseek/deepseek-chat-v3-0324:free