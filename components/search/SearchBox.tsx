'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';

interface SearchBoxProps {
  onSearch: (query: string) => void;
  placeholder?: string;
  isLoading?: boolean;
  initialValue?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export default function SearchBox({ 
  onSearch, 
  placeholder = "Search...", 
  isLoading = false,
  initialValue = '',
  size = 'md',
  className = ''
}: SearchBoxProps) {
  const [query, setQuery] = useState(initialValue);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      onSearch(query.trim());
    }
  };

  const sizeClasses = {
    sm: {
      input: 'pl-10 pr-4 py-2 text-sm',
      button: 'px-4 py-1',
      icon: 'w-4 h-4',
      iconPosition: 'left-3'
    },
    md: {
      input: 'pl-12 pr-4 py-3 text-base',
      button: 'px-4 py-1',
      icon: 'w-5 h-5',
      iconPosition: 'left-4'
    },
    lg: {
      input: 'pl-12 pr-4 py-4 text-lg',
      button: 'px-6 py-2',
      icon: 'w-5 h-5',
      iconPosition: 'left-4'
    }
  };

  const currentSize = sizeClasses[size];

  return (
    <form onSubmit={handleSubmit} className={`relative ${className}`}>
      <div className="relative">
        <Search className={`absolute ${currentSize.iconPosition} top-1/2 transform -translate-y-1/2 text-slate-400 ${currentSize.icon}`} />
        <Input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder={placeholder}
          className={`${currentSize.input} border-2 border-slate-200 rounded-full focus:border-[#FF6800] focus:ring-[#FF6800] shadow-lg`}
          disabled={isLoading}
        />
      </div>
      <Button
        type="submit"
        disabled={!query.trim() || isLoading}
        className={`absolute right-2 top-1/2 transform -translate-y-1/2 bg-[#FF6800] hover:bg-[#e55a00] rounded-full ${currentSize.button}`}
      >
        {isLoading ? (
          <div className="flex items-center gap-2">
            <div className={`${currentSize.icon} border-2 border-white border-t-transparent rounded-full animate-spin`} />
            {size === 'lg' && <span>Searching...</span>}
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <Search className={currentSize.icon} />
            {size === 'lg' && <span>Search</span>}
          </div>
        )}
      </Button>
    </form>
  );
}
