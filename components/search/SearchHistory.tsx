'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  TrendingUp, 
  Search,
  ArrowRight,
  Sparkles
} from 'lucide-react';
import { getRecentSearchQueries, getPopularSearchQueries } from '@/lib/search-history';

interface SearchHistoryProps {
  onSuggestionClick: (suggestion: string) => void;
  className?: string;
}

export default function SearchHistory({ onSuggestionClick, className = '' }: SearchHistoryProps) {
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [popularSearches, setPopularSearches] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadSearchHistory();
  }, []);

  const loadSearchHistory = async () => {
    try {
      setIsLoading(true);
      
      // Load both recent and popular searches in parallel
      const [recent, popular] = await Promise.all([
        getRecentSearchQueries(6),
        getPopularSearchQueries(8)
      ]);

      setRecentSearches(recent);
      setPopularSearches(popular);
    } catch (error) {
      console.error('Error loading search history:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Don't render anything if we're loading or have no suggestions
  if (isLoading) {
    return (
      <div className={`mb-8 ${className}`}>
        <div className="flex items-center justify-center py-4">
          <div className="flex items-center gap-2 text-slate-500">
            <div className="w-4 h-4 border-2 border-slate-300 border-t-[#FF6800] rounded-full animate-spin" />
            <span className="text-sm">Loading suggestions...</span>
          </div>
        </div>
      </div>
    );
  }

  // If no recent searches and no popular searches, don't render the component
  if (recentSearches.length === 0 && popularSearches.length === 0) {
    return null;
  }

  return (
    <div className={`mb-8 ${className}`}>
      <Card className="border-slate-200 shadow-sm">
        <CardContent className="p-6">
          {/* Recent Searches */}
          {recentSearches.length > 0 && (
            <div className="mb-6">
              <h3 className="font-semibold text-slate-800 mb-3 flex items-center gap-2">
                <Clock className="w-4 h-4 text-slate-600" />
                Recent Searches
              </h3>
              <div className="flex flex-wrap gap-2">
                {recentSearches.map((query, index) => (
                  <Button
                    key={`recent-${index}`}
                    variant="outline"
                    size="sm"
                    onClick={() => onSuggestionClick(query)}
                    className="text-sm rounded-full border-slate-300 hover:border-[#FF6800] hover:text-[#FF6800] transition-colors"
                  >
                    <Search className="w-3 h-3 mr-1" />
                    {query}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Popular Searches */}
          {popularSearches.length > 0 && (
            <div>
              <h3 className="font-semibold text-slate-800 mb-3 flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-[#FF6800]" />
                Popular Searches
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {popularSearches.map((query, index) => (
                  <Button
                    key={`popular-${index}`}
                    variant="outline"
                    size="sm"
                    onClick={() => onSuggestionClick(query)}
                    className="justify-start text-left h-auto py-2 px-3 hover:border-[#FF6800] hover:text-[#FF6800] transition-colors"
                  >
                    <span className="truncate flex-1">{query}</span>
                    <ArrowRight className="w-3 h-3 ml-1 flex-shrink-0" />
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Quick Search Tips */}
          <div className="mt-6 pt-4 border-t border-slate-200">
            <div className="flex items-center gap-2 text-xs text-slate-500">
              <Sparkles className="w-3 h-3" />
              <span>Try searching for specific topics like "cara daftar", "syarat dokumen", or "jadwal pendaftaran"</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
