// Direct test of the deletion API endpoint
const http = require('http');

const testSourceId = '0da6c819-f57b-4b2b-921c-374ef76bff08';
const userId = '8e8e914b-f5e5-4294-8148-11e19029e05d';

// Create a mock JWT token for testing (this is just for local testing)
// In a real scenario, you'd get this from the Supabase auth
const mockToken = 'test-token';

const options = {
  hostname: 'localhost',
  port: 3000,
  path: `/api/knowledge-sources/${testSourceId}`,
  method: 'DELETE',
  headers: {
    'Authorization': `Bearer ${mockToken}`,
    'Content-Type': 'application/json'
  }
};

console.log(`Testing deletion of knowledge source: ${testSourceId}`);
console.log(`API endpoint: http://localhost:3000${options.path}`);

const req = http.request(options, (res) => {
  console.log(`Response status: ${res.statusCode}`);
  console.log(`Response headers:`, res.headers);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('Response body:', data);
    try {
      const jsonData = JSON.parse(data);
      console.log('Parsed response:', jsonData);
    } catch (e) {
      console.log('Response is not valid JSON');
    }
  });
});

req.on('error', (e) => {
  console.error(`Request error: ${e.message}`);
});

req.end();
