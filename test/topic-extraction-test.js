/**
 * Test file for the optimized topic extraction algorithm
 * This tests the enhanced extractTopicsFromContent function
 */

// Mock the SupabaseVectorStore class to test the private method
class MockSupabaseVectorStore {
  // Copy of the optimized extractTopicsFromContent method for testing
  extractTopicsFromContent(content, title = '') {
    const text = `${title} ${content}`.toLowerCase();
    const originalText = `${title} ${content}`;
    const candidateTopics = new Map();
    
    // Enhanced generic terms filtering with more comprehensive blacklist
    const genericTerms = new Set([
      'the', 'and', 'for', 'are', 'with', 'this', 'that', 'from', 'they', 'have', 
      'will', 'been', 'were', 'said', 'each', 'which', 'their', 'time', 'would', 
      'there', 'could', 'other', 'more', 'very', 'what', 'know', 'just', 'first', 
      'into', 'over', 'think', 'also', 'your', 'work', 'life', 'only', 'can', 
      'still', 'should', 'after', 'being', 'now', 'made', 'before', 'here', 
      'through', 'when', 'where', 'much', 'some', 'these', 'many', 'then', 
      'them', 'well', 'were', 'information', 'content', 'text', 'data', 'system',
      'website', 'page', 'section', 'part', 'item', 'list', 'number', 'name',
      // Additional generic terms for better filtering
      'additional', 'various', 'multiple', 'several', 'different', 'including', 
      'related', 'following', 'based', 'using', 'such', 'example', 'examples',
      'important', 'available', 'possible', 'general', 'specific', 'common',
      'basic', 'main', 'primary', 'secondary', 'current', 'new', 'old', 'good',
      'best', 'better', 'great', 'small', 'large', 'high', 'low', 'long', 'short'
    ]);

    // Helper function to add topic with scoring
    const addTopic = (topic, source, baseScore = 1) => {
      if (topic.length < 4 || topic.length > 30 || genericTerms.has(topic) || /^\d+$/.test(topic)) {
        return;
      }
      
      if (!candidateTopics.has(topic)) {
        candidateTopics.set(topic, { frequency: 0, score: 0, sources: new Set() });
      }
      
      const topicData = candidateTopics.get(topic);
      topicData.frequency += 1;
      topicData.sources.add(source);
      
      // Quality scoring: favor compound terms and title presence
      let qualityScore = baseScore;
      if (topic.includes(' ')) qualityScore *= 1.5; // Compound terms bonus
      if (title.toLowerCase().includes(topic)) qualityScore *= 1.3; // Title presence bonus
      
      topicData.score += qualityScore;
    };

    // Extract proper nouns and capitalized terms with frequency tracking
    const properNouns = originalText.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g) || [];
    properNouns.forEach(noun => {
      const normalized = noun.toLowerCase();
      addTopic(normalized, 'proper_noun', 2);
    });

    // Extract acronyms with frequency tracking
    const acronyms = originalText.match(/\b[A-Z]{2,10}\b/g) || [];
    acronyms.forEach(acronym => {
      if (acronym.length >= 2 && acronym.length <= 6) {
        addTopic(acronym.toLowerCase(), 'acronym', 1.5);
      }
    });

    // Extract meaningful compound words with frequency tracking
    const phrases = text.match(/\b\w+[-_]\w+(?:[-_]\w+)*\b/g) || [];
    phrases.forEach(phrase => {
      if (phrase.length > 4 && phrase.length < 25) {
        const normalized = phrase.replace(/[-_]/g, ' ');
        addTopic(normalized, 'compound', 1.8);
      }
    });

    // Extract important terms that appear frequently with enhanced filtering
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 20);
    
    sentences.forEach(sentence => {
      const words = sentence.split(/\s+/).filter(word => 
        word.length > 3 && 
        word.length < 20 &&
        !/^\d+$/.test(word) &&
        /^[a-zA-Z]+$/.test(word)
      );
      
      words.forEach(word => {
        addTopic(word, 'frequent_term', 1);
      });
    });

    // Filter and rank topics based on frequency and quality score
    const qualifiedTopics = Array.from(candidateTopics.entries())
      .filter(([topic, data]) => 
        data.frequency >= 2 && // Must appear at least 2 times
        !genericTerms.has(topic) && 
        topic.length >= 4 && 
        topic.length <= 30 &&
        !/^\d+$/.test(topic)
      )
      .sort((a, b) => {
        // Sort by combined score (frequency * quality score)
        const scoreA = a[1].frequency * a[1].score;
        const scoreB = b[1].frequency * b[1].score;
        return scoreB - scoreA;
      })
      .slice(0, 10) // Reduced from 20 to 10
      .map(([topic]) => topic);

    return qualifiedTopics;
  }
}

// Test cases
function runTests() {
  const vectorStore = new MockSupabaseVectorStore();
  
  console.log('🧪 Testing Optimized Topic Extraction Algorithm\n');
  
  // Test 1: Academic document
  const academicTitle = "Machine Learning Applications in Natural Language Processing";
  const academicContent = `
    Machine learning has revolutionized natural language processing in recent years. 
    Deep learning models, particularly transformer architectures, have shown remarkable 
    performance in various NLP tasks. BERT and GPT models have become foundational 
    technologies in the field. These models utilize attention mechanisms to understand 
    contextual relationships in text. Natural language processing applications include 
    sentiment analysis, machine translation, and text summarization. The transformer 
    architecture has enabled significant breakthroughs in language understanding.
    Deep learning techniques continue to advance the state-of-the-art in NLP research.
  `;
  
  console.log('📚 Test 1: Academic Document');
  console.log('Title:', academicTitle);
  const academicTopics = vectorStore.extractTopicsFromContent(academicContent, academicTitle);
  console.log('Topics:', academicTopics);
  console.log('Topic count:', academicTopics.length);
  console.log('✅ Expected: 8-10 high-quality topics related to ML/NLP\n');
  
  // Test 2: Business document
  const businessTitle = "Digital Marketing Strategy for E-commerce Growth";
  const businessContent = `
    Digital marketing strategies are essential for e-commerce business growth. 
    Social media marketing, search engine optimization, and content marketing 
    form the foundation of successful online businesses. Customer acquisition 
    costs must be balanced with customer lifetime value. Email marketing campaigns 
    provide excellent return on investment for e-commerce companies. Social media 
    platforms like Facebook, Instagram, and Twitter offer targeted advertising 
    opportunities. Search engine optimization improves organic traffic and reduces 
    customer acquisition costs. Content marketing builds brand awareness and 
    customer engagement across digital channels.
  `;
  
  console.log('💼 Test 2: Business Document');
  console.log('Title:', businessTitle);
  const businessTopics = vectorStore.extractTopicsFromContent(businessContent, businessTitle);
  console.log('Topics:', businessTopics);
  console.log('Topic count:', businessTopics.length);
  console.log('✅ Expected: 8-10 topics related to digital marketing\n');
  
  // Test 3: Short document (edge case)
  const shortTitle = "Quick Guide";
  const shortContent = "This is a very short document with minimal content for testing purposes.";
  
  console.log('📄 Test 3: Short Document (Edge Case)');
  console.log('Title:', shortTitle);
  const shortTopics = vectorStore.extractTopicsFromContent(shortContent, shortTitle);
  console.log('Topics:', shortTopics);
  console.log('Topic count:', shortTopics.length);
  console.log('✅ Expected: Few or no topics due to insufficient content\n');
  
  // Test 4: Document with generic terms
  const genericTitle = "General Information";
  const genericContent = `
    This document contains various information about different topics. 
    There are many examples of general content that includes basic information.
    The content is very general and contains multiple references to common terms.
    Various sections provide different examples of general information.
    This content should produce minimal high-quality topics due to generic nature.
  `;
  
  console.log('🔍 Test 4: Generic Content');
  console.log('Title:', genericTitle);
  const genericTopics = vectorStore.extractTopicsFromContent(genericContent, genericTitle);
  console.log('Topics:', genericTopics);
  console.log('Topic count:', genericTopics.length);
  console.log('✅ Expected: Very few topics due to generic content filtering\n');
  
  // Summary
  console.log('📊 Test Summary:');
  console.log(`Academic document: ${academicTopics.length} topics`);
  console.log(`Business document: ${businessTopics.length} topics`);
  console.log(`Short document: ${shortTopics.length} topics`);
  console.log(`Generic document: ${genericTopics.length} topics`);
  console.log('\n✅ All tests completed! Check that topic counts are 8-12 for substantial documents.');
}

// Run the tests
runTests();
