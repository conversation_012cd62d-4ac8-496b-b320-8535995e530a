'use client';

import { useState, useEffect } from 'react';
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import Head from 'next/head';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import {
  Search,
  Bot,
  Brain,
  Settings,
  Shield,
  LogOut,
  ArrowLeft,
  ExternalLink,
  CheckCircle,
  XCircle,
  Clock,
  Copy,
  Check
} from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';
import { AuthService } from '@/lib/auth';
import SearchBox from '@/components/search/SearchBox';
import { saveSearchQuery } from '@/lib/search-history';

interface SearchResultItem {
  id: string;
  title: string;
  content: string;
  score: number;
  matchType: string;
  source: string;
  sourceUrl?: string;
  type: string;
  topics: string[];
  addedAt: string;
  relevanceScore: number;
}

interface SearchResponse {
  results: SearchResultItem[];
  totalResults: number;
  query: string;
  aiSummary?: string;
  sources: string[];
  sourceUrls?: { [title: string]: string };
  searchScores: string[];
  embeddingType: string;
  timestamp: string;
  message?: string;
  suggestions?: string[];
}

export default function SearchPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [user, setUser] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResponse, setSearchResponse] = useState<SearchResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [copiedText, setCopiedText] = useState(false);

  useEffect(() => {
    // Get query from URL parameters
    const query = searchParams.get('q');
    if (query) {
      setSearchQuery(query);
      performSearch(query);
    }

    // Check authentication status
    const checkAuth = async () => {
      const currentUser = await AuthService.getCurrentUser();
      setUser(currentUser);
    };
    checkAuth();

    // Listen for auth changes
    const { data: { subscription } } = AuthService.onAuthStateChange((user) => {
      setUser(user);
    });

    return () => subscription.unsubscribe();
  }, [searchParams]);

  const performSearch = async (query: string) => {
    if (!query.trim()) return;

    setIsLoading(true);
    try {
      const response = await fetch('/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });

      if (response.status === 429) {
        const errorData = await response.json();
        const retryAfter = errorData.retryAfter || 300;
        const minutesRemaining = Math.ceil(retryAfter / 60);
        toast.error(`Rate limit exceeded. Please wait ${minutesRemaining} minute${minutesRemaining !== 1 ? 's' : ''} before searching again.`);
        return;
      }

      if (!response.ok) {
        throw new Error('Failed to perform search');
      }

      const data = await response.json();
      setSearchResponse(data);
    } catch (error) {
      console.error('Error performing search:', error);
      toast.error('Failed to perform search. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewSearch = async (query: string) => {
    // Save search query to history (async, don't wait for completion)
    saveSearchQuery(query, user?.id).catch(error => {
      console.error('Error saving search query to history:', error);
    });

    // Update URL with new query
    router.push(`/search?q=${encodeURIComponent(query)}`);
  };

  const handleSourceClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const handleCopyToClipboard = async (text: string) => {
    try {
      if (!navigator.clipboard) {
        throw new Error('Clipboard API not available');
      }

      await navigator.clipboard.writeText(text);
      setCopiedText(true);
      setTimeout(() => setCopiedText(false), 2000);
      toast.success('Copied to clipboard!');
    } catch (err) {
      console.error('Failed to copy text: ', err);
      toast.error('Failed to copy text to clipboard');
    }
  };

  const handleSignOut = async () => {
    try {
      await AuthService.signOut();
      setUser(null);
      toast.success('Successfully signed out!');
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('Failed to sign out');
    }
  };

  return (
    <div className="min-h-screen bg-[#fffefa] flex flex-col">
      {/* Header */}
      <div className="border-b border-slate-200 bg-white/80 backdrop-blur-sm sticky top-0 z-10">
        <div className="container mx-auto px-4 py-3 max-w-6xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Link href="/">
                <Button variant="ghost" size="sm" className="text-slate-600 hover:text-slate-800">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back
                </Button>
              </Link>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-[#FF6800] rounded-lg">
                  <Bot className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-slate-800">Tanya LPDP</h1>
                  <p className="text-slate-600 text-sm flex items-center gap-2">
                    <Brain className="w-3 h-3" />
                    Search Results
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {user ? (
                <>
                  <Badge variant="outline" className="hidden sm:flex items-center gap-1 text-xs">
                    <Shield className="w-3 h-3" />
                    Authenticated
                  </Badge>
                  <Link href="/admin">
                    <Button variant="outline" size="sm">
                      <Settings className="w-4 h-4 mr-2" />
                      Admin Panel
                    </Button>
                  </Link>
                  <Button variant="outline" size="sm" onClick={handleSignOut}>
                    <LogOut className="w-4 h-4 mr-2" />
                    Sign Out
                  </Button>
                </>
              ) : null}
            </div>
          </div>
        </div>
      </div>

      {/* Search Bar */}
      <div className="border-b border-slate-200 bg-white">
        <div className="container mx-auto px-4 py-4 max-w-4xl">
          <SearchBox
            onSearch={handleNewSearch}
            placeholder="Search for information..."
            isLoading={isLoading}
            initialValue={searchQuery}
            size="md"
          />
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 container mx-auto px-4 py-6 max-w-6xl">
        {isLoading && !searchResponse && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="w-8 h-8 border-4 border-[#FF6800] border-t-transparent rounded-full animate-spin mx-auto mb-4" />
              <p className="text-slate-600">Searching knowledge base...</p>
            </div>
          </div>
        )}

        {searchResponse && (
          <div className="space-y-6">
            {/* Search Info */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-sm text-slate-500">
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>Search completed</span>
                </div>
                <div className="flex items-center gap-1">
                  <span>{searchResponse.totalResults} results found</span>
                </div>
              </div>
              <div className="text-sm text-slate-500">
                Query: "{searchResponse.query}"
              </div>
            </div>

            {/* AI Summary */}
            {searchResponse.aiSummary && (
              <Card className="border-slate-200 shadow-sm bg-gradient-to-r from-orange-50 to-amber-50">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <Brain className="w-5 h-5 text-[#FF6800]" />
                      <span className="font-medium text-slate-800">AI Summary</span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCopyToClipboard(searchResponse.aiSummary!)}
                      className="text-slate-500 hover:text-slate-700"
                    >
                      {copiedText ? (
                        <>
                          <Check className="w-4 h-4 mr-2 text-green-500" />
                          Copied!
                        </>
                      ) : (
                        <>
                          <Copy className="w-4 h-4 mr-2" />
                          Copy
                        </>
                      )}
                    </Button>
                  </div>

                  <div className="prose prose-slate max-w-none">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={{
                        a: (props) => (
                          <a className="text-[#FF6800] hover:underline" target="_blank" rel="noopener noreferrer" {...props}>
                            {props.children}
                          </a>
                        ),
                      }}
                    >
                      {searchResponse.aiSummary}
                    </ReactMarkdown>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Search Results */}
            {searchResponse.results.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-slate-800 flex items-center gap-2">
                  <Search className="w-5 h-5 text-[#FF6800]" />
                  Search Results ({searchResponse.totalResults})
                </h3>

                {searchResponse.results.map((result, index) => (
                  <Card key={result.id} className="border-slate-200 shadow-sm hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h4 className="font-medium text-slate-800 mb-2 flex items-center gap-2">
                            {result.sourceUrl ? (
                              <Button
                                variant="link"
                                className="p-0 h-auto font-medium text-[#FF6800] hover:underline"
                                onClick={() => handleSourceClick(result.sourceUrl!)}
                              >
                                {result.title}
                                <ExternalLink className="w-4 h-4 ml-1" />
                              </Button>
                            ) : (
                              <span>{result.title}</span>
                            )}
                          </h4>
                          <div className="flex items-center gap-3 text-sm text-slate-500 mb-3">
                            <Badge variant="outline" className="text-xs">
                              {result.relevanceScore}% match
                            </Badge>
                            <span className="capitalize">{result.matchType} search</span>
                            <span>{result.type.toUpperCase()}</span>
                          </div>
                        </div>
                      </div>

                      <div className="text-slate-700 mb-4 line-clamp-3">
                        {result.content.length > 300
                          ? `${result.content.substring(0, 300)}...`
                          : result.content
                        }
                      </div>

                      {result.topics.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-3">
                          {result.topics.slice(0, 5).map((topic, topicIndex) => (
                            <Badge key={topicIndex} variant="secondary" className="text-xs">
                              {topic}
                            </Badge>
                          ))}
                          {result.topics.length > 5 && (
                            <Badge variant="secondary" className="text-xs">
                              +{result.topics.length - 5} more
                            </Badge>
                          )}
                        </div>
                      )}

                      <div className="flex items-center justify-between">
                        <div className="text-xs text-slate-500">
                          Added: {new Date(result.addedAt).toLocaleDateString()}
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCopyToClipboard(result.content)}
                          className="text-xs"
                        >
                          <Copy className="w-3 h-3 mr-1" />
                          Copy
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}

        {!isLoading && searchResponse && searchResponse.totalResults === 0 && (
          <div className="text-center py-12">
            <div className="mb-6">
              <XCircle className="w-16 h-16 text-slate-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-slate-700 mb-2">No Results Found</h3>
              <p className="text-slate-600 mb-4">
                {searchResponse.message || `No results found for "${searchResponse.query}"`}
              </p>
            </div>

            {searchResponse.suggestions && searchResponse.suggestions.length > 0 && (
              <div className="max-w-2xl mx-auto">
                <h4 className="font-medium text-slate-700 mb-3">Try searching for these topics instead:</h4>
                <div className="flex flex-wrap gap-2 justify-center">
                  {searchResponse.suggestions.map((suggestion, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSearchQuery(suggestion);
                        router.push(`/search?q=${encodeURIComponent(suggestion)}`);
                      }}
                      className="rounded-full"
                    >
                      {suggestion}
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {!isLoading && !searchResponse && searchParams.get('q') && (
          <div className="text-center py-12">
            <p className="text-slate-600">No results found. Please try a different search query.</p>
          </div>
        )}
      </div>
    </div>
  );
}
