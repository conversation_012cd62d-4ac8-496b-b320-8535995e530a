import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Search Results - Tanya LPDP',
  description: 'Search results for LPDP scholarship information from curated sources',
  keywords: 'LPDP, beasiswa, scholarship, search, Indonesia, pendidikan',
  openGraph: {
    title: 'Search Results - Tanya LPDP',
    description: 'Search results for LPDP scholarship information from curated sources',
    type: 'website',
  },
};

export default function SearchLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
