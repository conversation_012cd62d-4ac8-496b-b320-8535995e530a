'use client';

import { Suspense, useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, Loader2, Bot } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import Link from 'next/link';

function ConfirmContent() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const handleEmailConfirmation = async () => {
      try {
        // Get tokens from URL hash (for direct token URLs)
        const hash = window.location.hash.substring(1);
        const params = new URLSearchParams(hash);
        
        const access_token = params.get('access_token');
        const refresh_token = params.get('refresh_token');
        
        // Also check URL search params (for code-based URLs)
        const code = searchParams.get('code');
        const error = searchParams.get('error');

        if (error) {
          setStatus('error');
          setMessage('Email confirmation failed. Please try again.');
          return;
        }

        if (code) {
          // Handle code-based confirmation
          const { error: exchangeError } = await supabase.auth.exchangeCodeForSession(code);
          
          if (exchangeError) {
            console.error('Error exchanging code:', exchangeError);
            setStatus('error');
            setMessage('Failed to confirm email. Please try again.');
            return;
          }
        } else if (access_token && refresh_token) {
          // Handle token-based confirmation
          const { error: sessionError } = await supabase.auth.setSession({
            access_token,
            refresh_token
          });
          
          if (sessionError) {
            console.error('Error setting session:', sessionError);
            setStatus('error');
            setMessage('Failed to confirm email. Please try again.');
            return;
          }
        } else {
          setStatus('error');
          setMessage('Invalid confirmation link. Please check your email for the correct link.');
          return;
        }

        // Success
        setStatus('success');
        setMessage('Email confirmed successfully! Redirecting to home...');

        // Redirect after a short delay
        setTimeout(() => {
          router.push('/');
        }, 2000);

      } catch (error) {
        console.error('Confirmation error:', error);
        setStatus('error');
        setMessage('An unexpected error occurred. Please try again.');
      }
    };

    handleEmailConfirmation();
  }, [searchParams, router]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="mx-auto mb-4 p-3 bg-blue-500 rounded-full w-fit">
            <Bot className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-slate-800 mb-2">Email Confirmation</h1>
          <p className="text-slate-600">Confirming your email address</p>
        </div>

        <Card className="border-slate-200 shadow-lg">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2">
              {status === 'loading' && (
                <>
                  <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
                  Confirming Email
                </>
              )}
              {status === 'success' && (
                <>
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  Email Confirmed
                </>
              )}
              {status === 'error' && (
                <>
                  <XCircle className="w-5 h-5 text-red-500" />
                  Confirmation Failed
                </>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className={`text-sm ${
              status === 'success' ? 'text-green-600' : 
              status === 'error' ? 'text-red-600' : 
              'text-slate-600'
            }`}>
              {message}
            </p>

            {status === 'loading' && (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              </div>
            )}

            {status === 'success' && (
              <div className="space-y-2">
                <p className="text-sm text-slate-600">
                  You will be redirected automatically, or click below:
                </p>
                <Link href="/">
                  <Button className="w-full">
                    Go to Home
                  </Button>
                </Link>
              </div>
            )}

            {status === 'error' && (
              <div className="space-y-2">
                <Link href="/auth/signin">
                  <Button variant="outline" className="w-full">
                    Back to Sign In
                  </Button>
                </Link>
                <p className="text-xs text-slate-500">
                  Need help? Contact support or try signing up again.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default function ConfirmPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <div className="mx-auto mb-4 p-3 bg-blue-500 rounded-full w-fit">
              <Bot className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-slate-800 mb-2">Email Confirmation</h1>
            <p className="text-slate-600">Loading confirmation...</p>
          </div>
          <div className="bg-white rounded-lg shadow-lg p-6 text-center">
            <Loader2 className="w-8 h-8 mx-auto animate-spin text-blue-500 mb-4" />
            <p className="text-slate-600">Processing your confirmation...</p>
          </div>
        </div>
      </div>
    }>
      <ConfirmContent />
    </Suspense>
  );
}