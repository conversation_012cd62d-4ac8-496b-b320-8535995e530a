import { NextRequest, NextResponse } from 'next/server';
import { SupabaseVectorStore } from '@/lib/supabase-vector-store';
import { supabase } from '@/lib/supabase';

// Real URL processing with web scraping
async function processURL(url: string): Promise<{ title: string; content: string; topics: string[] }> {
  try {
    console.log(`🌐 Fetching content from: ${url}`);
    
    // Create AbortController for timeout handling
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, 10000); // 10 second timeout
    
    try {
      // Fetch the webpage with proper timeout handling
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
        },
        signal: controller.signal,
      });

      // Clear timeout since fetch completed
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const html = await response.text();
      console.log(`✅ Fetched ${html.length} characters from ${url}`);

      // Extract content using simple text processing
      const content = extractContentFromHTML(html);
      const title = extractTitleFromHTML(html, url);
      
      console.log(`📄 Extracted title: "${title}"`);
      console.log(`📝 Extracted content: ${content.length} characters`);

      return {
        title,
        content,
        topics: [] // Topics will be extracted by the vector store
      };
    } catch (fetchError) {
      // Clear timeout in case of error
      clearTimeout(timeoutId);
      throw fetchError;
    }
    
  } catch (error) {
    console.error(`❌ Error processing URL ${url}:`, error);
    
    // Fallback to basic content extraction if fetch fails
    const domain = new URL(url).hostname;
    const title = `Website Content - ${domain}`;
    const content = `Content from ${url}

This document contains information from ${domain}. The content could not be automatically extracted due to technical limitations, but the URL has been saved for reference.

To access the full content, please visit: ${url}

This source has been added to the knowledge base and can be referenced in future queries.`;

    return { title, content, topics: [] };
  }
}

// Extract readable content from HTML
function extractContentFromHTML(html: string): string {
  try {
    // Remove script and style tags
    let content = html.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');
    content = content.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
    content = content.replace(/<noscript[^>]*>[\s\S]*?<\/noscript>/gi, '');
    
    // Remove HTML comments
    content = content.replace(/<!--[\s\S]*?-->/g, '');
    
    // Extract text from common content containers
    const contentPatterns = [
      /<main[^>]*>([\s\S]*?)<\/main>/gi,
      /<article[^>]*>([\s\S]*?)<\/article>/gi,
      /<div[^>]*class="[^"]*content[^"]*"[^>]*>([\s\S]*?)<\/div>/gi,
      /<div[^>]*class="[^"]*main[^"]*"[^>]*>([\s\S]*?)<\/div>/gi,
      /<section[^>]*>([\s\S]*?)<\/section>/gi,
      /<div[^>]*id="[^"]*content[^"]*"[^>]*>([\s\S]*?)<\/div>/gi,
    ];
    
    let extractedContent = '';
    
    // Try to extract from content containers first
    for (const pattern of contentPatterns) {
      const matches = content.match(pattern);
      if (matches && matches.length > 0) {
        extractedContent = matches.join('\n\n');
        break;
      }
    }
    
    // If no content containers found, extract from body
    if (!extractedContent) {
      const bodyMatch = content.match(/<body[^>]*>([\s\S]*?)<\/body>/gi);
      if (bodyMatch) {
        extractedContent = bodyMatch[0];
      } else {
        extractedContent = content;
      }
    }
    
    // Remove remaining HTML tags
    extractedContent = extractedContent.replace(/<[^>]+>/g, ' ');
    
    // Decode HTML entities
    extractedContent = extractedContent
      .replace(/&nbsp;/g, ' ')
      .replace(/&/g, '&')
      .replace(/</g, '<')
      .replace(/>/g, '>')
      .replace(/"/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&apos;/g, "'");
    
    // Clean up whitespace
    extractedContent = extractedContent
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n\n')
      .trim();
    
    // Filter out navigation and footer content
    const lines = extractedContent.split('\n');
    const filteredLines = lines.filter(line => {
      const lowerLine = line.toLowerCase().trim();
      
      if (lowerLine.length < 10) return false;
      if (lowerLine.includes('cookie')) return false;
      if (lowerLine.includes('privacy policy')) return false;
      if (lowerLine.includes('terms of service')) return false;
      if (lowerLine.includes('copyright')) return false;
      if (lowerLine.includes('all rights reserved')) return false;
      if (lowerLine.match(/^(home|about|contact|login|register|menu)$/)) return false;
      
      return true;
    });
    
    const finalContent = filteredLines.join('\n').trim();
    
    if (finalContent.length < 100) {
      throw new Error('Insufficient content extracted');
    }
    
    return finalContent;
    
  } catch (error) {
    console.error('Error extracting content from HTML:', error);
    
    // Fallback: simple tag removal
    let fallbackContent = html
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
      .replace(/<[^>]+>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
    
    return fallbackContent.substring(0, 2000) + (fallbackContent.length > 2000 ? '...' : '');
  }
}

// Extract title from HTML
function extractTitleFromHTML(html: string, url: string): string {
  try {
    // Try to extract title tag
    const titleMatch = html.match(/<title[^>]*>(.*?)<\/title>/i);
    if (titleMatch && titleMatch[1]) {
      let title = titleMatch[1]
        .replace(/&nbsp;/g, ' ')
        .replace(/&/g, '&')
        .replace(/</g, '<')
        .replace(/>/g, '>')
        .replace(/"/g, '"')
        .replace(/&#39;/g, "'")
        .trim();
      
      if (title.length > 5 && title.length < 200) {
        return title;
      }
    }
    
    // Try to extract h1 tag
    const h1Match = html.match(/<h1[^>]*>(.*?)<\/h1>/i);
    if (h1Match && h1Match[1]) {
      let h1Title = h1Match[1].replace(/<[^>]+>/g, '').trim();
      if (h1Title.length > 5 && h1Title.length < 200) {
        return h1Title;
      }
    }
    
    // Fallback to domain name
    const domain = new URL(url).hostname;
    return `Website Content - ${domain}`;
    
  } catch (error) {
    console.error('Error extracting title:', error);
    const domain = new URL(url).hostname;
    return `Website Content - ${domain}`;
  }
}

function isValidURL(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the auth token from the Authorization header
    const authHeader = request.headers.get('Authorization');
    const token = authHeader?.split(' ')[1];
    
    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }
    
    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      console.error('Auth error:', authError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { url } = await request.json();
    
    if (!url || typeof url !== 'string') {
      return NextResponse.json(
        { error: 'Invalid URL provided' },
        { status: 400 }
      );
    }
    
    if (!isValidURL(url)) {
      return NextResponse.json(
        { error: 'Invalid URL format' },
        { status: 400 }
      );
    }
    
    // Check if URL is accessible (basic validation)
    const urlObj = new URL(url);
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return NextResponse.json(
        { error: 'Only HTTP and HTTPS URLs are supported' },
        { status: 400 }
      );
    }
    
    console.log(`🚀 Starting URL processing for: ${url}`);
    
    // Process the URL with real web scraping
    const { title, content, topics } = await processURL(url);
    
    if (content.length < 50) {
      return NextResponse.json(
        { error: 'Unable to extract meaningful content from the URL. Please check if the URL is accessible and contains text content.' },
        { status: 400 }
      );
    }
    
    console.log('📥 Starting to add document to vector store');
    console.log('👤 User ID:', user.id);
    
    try {
      // Add to Supabase vector store with chunking for large content
      const vectorStore = new SupabaseVectorStore(user.id, token);
      
      const documentId = `url_${Date.now()}`;
      console.log('📄 Creating document with ID:', documentId);
      
      await vectorStore.addDocumentWithChunking({
        id: documentId,
        content,
        metadata: {
          title,
          source: url,
          sourceUrl: url, // For URLs, sourceUrl is the same as source
          type: 'url',
          topics: topics, // Empty - will be extracted from content
          addedAt: new Date().toISOString()
        }
      }, user.id);
      
      console.log('✅ Successfully added document to vector store');
      
      const stats = await vectorStore.getStats(user.id);
      
      console.log(`✅ Successfully processed URL: ${title} (${content.length} chars)`);
      
      return NextResponse.json({
        success: true,
        title,
        content,
        topics: [], // Will be extracted by vector store
        url,
        embeddingType: stats.embeddingType
      });
    } catch (error) {
      console.error('❌ Error in vector store operation:', error);
      if (error instanceof Error) {
        console.error('Error details:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        });
      }
      throw error; // Re-throw to be caught by the outer try-catch
    }
    
  } catch (error) {
    console.error('URL processing error:', error);
    return NextResponse.json(
      { error: 'Failed to process URL. Please check if the URL is accessible and try again.' },
      { status: 500 }
    );
  }
}