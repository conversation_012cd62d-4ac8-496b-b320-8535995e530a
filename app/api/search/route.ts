import { NextRequest, NextResponse } from 'next/server';
import { OpenRouterClient } from '@/lib/openrouter';
import { SupabaseVectorStore } from '@/lib/supabase-vector-store';
import { SecurityUtils, SECURITY_CONFIG } from '@/lib/security-config';
import { saveSearchQuery } from '@/lib/search-history';

export const dynamic = 'force-dynamic';

// Get client IP for logging
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');

  if (cfConnectingIP) return cfConnectingIP;
  if (realIP) return realIP;
  if (forwarded) return forwarded.split(',')[0].trim();

  return 'unknown';
}

export async function POST(request: NextRequest) {
  const clientIP = getClientIP(request);

  try {
    const { query } = await request.json();

    // Input validation and sanitization
    if (!query || typeof query !== 'string') {
      SecurityUtils.logSecurityEvent({
        type: 'suspicious_activity',
        ip: clientIP,
        endpoint: '/api/search',
        details: { reason: 'Invalid query format', query: typeof query }
      });

      return NextResponse.json(
        { error: 'Invalid query format' },
        { status: 400 }
      );
    }

    // Sanitize input
    const sanitizedQuery = SecurityUtils.sanitizeInput(query);

    if (sanitizedQuery.length === 0) {
      return NextResponse.json(
        { error: 'Query cannot be empty' },
        { status: 400 }
      );
    }

    if (sanitizedQuery.length > SECURITY_CONFIG.CONTENT.MAX_QUERY_LENGTH) {
      return NextResponse.json(
        { error: `Query too long. Maximum ${SECURITY_CONFIG.CONTENT.MAX_QUERY_LENGTH} characters allowed.` },
        { status: 400 }
      );
    }

    console.log(`🔍 Processing search query: "${sanitizedQuery}"`);

    // Save search query to history (async, don't wait for completion)
    saveSearchQuery(sanitizedQuery).catch(error => {
      console.error('Error saving search query to history:', error);
    });

    // Initialize Supabase vector store with admin access (no user filtering)
    const vectorStore = new SupabaseVectorStore();

    // Get all documents from the knowledge base (public access)
    const allDocuments = await vectorStore.getAllDocuments();
    
    if (allDocuments.length === 0) {
      return NextResponse.json({
        results: [],
        totalResults: 0,
        query: sanitizedQuery,
        message: "No knowledge base sources have been added yet. The knowledge base is currently empty.",
        suggestions: []
      });
    }

    // Search for relevant documents using enhanced vector similarity
    const searchResults = await vectorStore.searchWithKeywords(sanitizedQuery, 10, 0.02);
    
    if (searchResults.length === 0) {
      // Get available information for user guidance
      const stats = await vectorStore.getStats();
      
      console.log(`❌ No search results found for: "${sanitizedQuery}"`);
      console.log(`📋 Available topics: ${stats.topics.slice(0, 10).join(', ')}`);
      console.log(`📄 Available sources: ${stats.sources.join(', ')}`);
      
      return NextResponse.json({
        results: [],
        totalResults: 0,
        query: sanitizedQuery,
        message: `No results found for "${sanitizedQuery}". Try searching for one of these topics instead.`,
        suggestions: stats.topics.slice(0, 8),
        availableSources: stats.sources.slice(0, 5)
      });
    }

    // Format search results for structured display
    const formattedResults = searchResults.map((result, index) => ({
      id: `result-${index}`,
      title: result.document.metadata.title,
      content: result.document.content,
      score: result.score,
      matchType: result.matchType,
      source: result.document.metadata.source,
      sourceUrl: result.document.metadata.sourceUrl,
      type: result.document.metadata.type,
      topics: result.document.metadata.topics,
      addedAt: result.document.metadata.addedAt,
      relevanceScore: Math.round(result.score * 100)
    }));

    // Collect unique sources and source URLs
    const sources = [...new Set(searchResults.map(result => result.document.metadata.title))];
    const sourceUrls: { [title: string]: string } = {};
    
    searchResults.forEach(result => {
      const title = result.document.metadata.title;
      const url = result.document.metadata.sourceUrl;
      if (url) {
        sourceUrls[title] = url;
      }
    });

    console.log(`✅ Found ${searchResults.length} relevant documents`);
    console.log(`📄 Sources: ${sources.join(', ')}`);

    // Generate AI summary if OpenRouter is available
    let aiSummary = null;
    const openRouterApiKey = process.env.OPENROUTER_API_KEY;
    
    if (openRouterApiKey && searchResults.length > 0) {
      try {
        const context = searchResults.slice(0, 3).map(result => 
          `Source: ${result.document.metadata.title}\nContent: ${result.document.content}`
        ).join('\n\n');

        const openRouter = new OpenRouterClient(openRouterApiKey);
        aiSummary = await openRouter.generateResponse(sanitizedQuery, context);
      } catch (aiError) {
        console.error('OpenRouter API error:', aiError);
        // Continue without AI summary
      }
    }

    const embeddingType = await vectorStore.getStats().then(stats => stats.embeddingType);

    return NextResponse.json({
      results: formattedResults,
      totalResults: searchResults.length,
      query: sanitizedQuery,
      aiSummary,
      sources: sources,
      sourceUrls: Object.keys(sourceUrls).length > 0 ? sourceUrls : undefined,
      searchScores: searchResults.map(r => (r.score * 100).toFixed(1)),
      embeddingType,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Search API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('q');

  if (!query) {
    return NextResponse.json(
      { error: 'Query parameter "q" is required' },
      { status: 400 }
    );
  }

  // Convert GET request to POST format
  return POST(new NextRequest(request.url, {
    method: 'POST',
    headers: request.headers,
    body: JSON.stringify({ query })
  }));
}
