import { NextRequest, NextResponse } from 'next/server';
import { OpenRouterClient } from '@/lib/openrouter';
import { SupabaseVectorStore } from '@/lib/supabase-vector-store';
import { ChatSessionsDB, ChatMessagesDB } from '@/lib/database';
import { supabase } from '@/lib/supabase';

export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    const { message, sessionId } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Invalid message format' },
        { status: 400 }
      );
    }

    // Get the auth token from the Authorization header
    const authHeader = request.headers.get('Authorization');
    const token = authHeader?.split(' ')[1];

    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log(`🔍 Processing query: "${message}" for user: ${user.id}`);

    // Initialize Supabase vector store
    const vectorStore = new SupabaseVectorStore(user.id);

    // Get all documents to check what's available
    const allDocuments = await vectorStore.getAllDocuments(user.id);
    
    if (allDocuments.length === 0) {
      return NextResponse.json({
        response: "I apologize, but no knowledge base sources have been added yet. Please add documents or URLs to the knowledge base through the admin panel.",
        fromKnowledgeBase: false,
        sessionId
      });
    }

    // Search for relevant documents using enhanced vector similarity
    const searchResults = await vectorStore.searchWithKeywords(message, 5, 0.02, user.id);
    
    if (searchResults.length === 0) {
      // Get available information for user guidance
      const stats = await vectorStore.getStats(user.id);
      
      console.log(`❌ No search results found for: "${message}"`);
      console.log(`📋 Available topics: ${stats.topics.slice(0, 10).join(', ')}`);
      console.log(`📄 Available sources: ${stats.sources.join(', ')}`);
      
      return NextResponse.json({
        response: `I couldn't find information related to your question in my knowledge base. 

Available sources include:
${stats.sources.map(source => `• ${source}`).join('\n')}

${stats.topics.length > 0 ? `Topics covered: ${stats.topics.slice(0, 10).join(', ')}${stats.topics.length > 10 ? '...' : ''}` : ''}

Please ask questions related to these sources and topics.`,
        fromKnowledgeBase: false,
        sessionId,
        availableSources: stats.sources,
        availableTopics: stats.topics.slice(0, 20)
      });
    }

    console.log(`✅ Found ${searchResults.length} relevant documents`);

    // Combine relevant context from search results
    const context = searchResults
      .map(result => `Source: ${result.document.metadata.title} (Match: ${result.matchType}, Score: ${result.score.toFixed(3)})
${result.document.content}`)
      .join('\n\n---\n\n');

    // Deduplicate source names and create source URL mapping
    const uniqueSources = [...new Set(searchResults.map(result => result.document.metadata.title))];
    const sources = uniqueSources.join(', ');
    
    // Create mapping of source titles to URLs
    const sourceUrls: { [title: string]: string } = {};
    searchResults.forEach(result => {
      const title = result.document.metadata.title;
      const sourceUrl = result.document.metadata.sourceUrl;
      if (sourceUrl && !sourceUrls[title]) {
        sourceUrls[title] = sourceUrl;
      }
    });

    // Check if OpenRouter API key is available
    const openRouterApiKey = process.env.OPENROUTER_API_KEY;
    
    let response: string;
    const embeddingType = await vectorStore.getStats(user.id).then(stats => stats.embeddingType);
    
    if (!openRouterApiKey) {
      // Fallback to simple response generation
      const bestMatch = searchResults[0];
      response = `Based on the information in my knowledge base:

${bestMatch.document.content.substring(0, 600)}${bestMatch.document.content.length > 600 ? '...' : ''}

Source: ${bestMatch.document.metadata.title}
Match Type: ${bestMatch.matchType}
Relevance Score: ${(bestMatch.score * 100).toFixed(1)}%`;
    } else {
      // Use OpenRouter for intelligent response generation
      try {
        const openRouter = new OpenRouterClient(openRouterApiKey);
        response = await openRouter.generateResponse(message, context);
      } catch (aiError) {
        console.error('OpenRouter API error:', aiError);
        
        // Fallback response with better formatting
        const bestMatch = searchResults[0];
        response = `Based on the information in my knowledge base:

${bestMatch.document.content.substring(0, 800)}${bestMatch.document.content.length > 800 ? '...' : ''}

Source: ${bestMatch.document.metadata.title}
Match Type: ${bestMatch.matchType}

Note: AI enhancement temporarily unavailable, showing direct knowledge base content.`;
      }
    }

    // Save chat message to database if sessionId provided
    if (sessionId) {
      try {
        // Save user message
        await ChatMessagesDB.create({
          text: message,
          sender: 'user',
          fromKnowledgeBase: false
        }, sessionId);

        // Save bot response
        await ChatMessagesDB.create({
          text: response,
          sender: 'bot',
          source: sources,
          sourceUrls: Object.keys(sourceUrls).length > 0 ? sourceUrls : undefined,
          fromKnowledgeBase: true,
          embeddingType
        }, sessionId);
      } catch (dbError) {
        console.error('Error saving chat messages:', dbError);
        // Continue without saving to database
      }
    }

    return NextResponse.json({
      response,
      fromKnowledgeBase: true,
      source: sources,
      sourceUrls: Object.keys(sourceUrls).length > 0 ? sourceUrls : undefined,
      sessionId,
      searchResults: searchResults.length,
      searchScores: searchResults.map(r => (r.score * 100).toFixed(1)),
      embeddingType
    });
    
  } catch (error) {
    console.error('Chat API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}