import { NextRequest, NextResponse } from 'next/server';
import { DocumentEmbeddingsDB } from '@/lib/database';
import { SupabaseVectorStore } from '@/lib/supabase-vector-store';
import { supabase } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    // Get the auth token from the Authorization header
    const authHeader = request.headers.get('Authorization');
    const token = authHeader?.split(' ')[1];

    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin (you might want to implement proper admin check)
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    const vectorStore = new SupabaseVectorStore(user.id);

    if (action === 'validate') {
      // Validate data integrity
      const validationResults = await vectorStore.validateDataIntegrity(user.id);
      
      return NextResponse.json({
        success: true,
        validation: validationResults,
        timestamp: new Date().toISOString()
      });
    }

    if (action === 'cleanup') {
      // Clean up invalid embeddings
      const cleanupResults = await DocumentEmbeddingsDB.deleteInvalidEmbeddings();
      
      return NextResponse.json({
        success: true,
        cleanup: cleanupResults,
        timestamp: new Date().toISOString()
      });
    }

    // Default: return validation results
    const validationResults = await vectorStore.validateDataIntegrity(user.id);
    
    return NextResponse.json({
      success: true,
      validation: validationResults,
      timestamp: new Date().toISOString(),
      availableActions: ['validate', 'cleanup']
    });

  } catch (error) {
    console.error('Data quality API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the auth token from the Authorization header
    const authHeader = request.headers.get('Authorization');
    const token = authHeader?.split(' ')[1];

    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { action } = await request.json();

    if (action === 'cleanup') {
      // Clean up invalid embeddings
      const cleanupResults = await DocumentEmbeddingsDB.deleteInvalidEmbeddings();
      
      return NextResponse.json({
        success: true,
        cleanup: cleanupResults,
        message: `Successfully cleaned up ${cleanupResults.deletedCount} invalid embeddings`,
        timestamp: new Date().toISOString()
      });
    }

    if (action === 'validate') {
      // Validate data integrity
      const vectorStore = new SupabaseVectorStore(user.id);
      const validationResults = await vectorStore.validateDataIntegrity(user.id);
      
      return NextResponse.json({
        success: true,
        validation: validationResults,
        timestamp: new Date().toISOString()
      });
    }

    return NextResponse.json(
      { error: 'Invalid action. Use "validate" or "cleanup"' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Data quality POST API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
