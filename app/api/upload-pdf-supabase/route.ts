import { NextRequest, NextResponse } from 'next/server';
import { SupabaseVectorStore } from '@/lib/supabase-vector-store';
import { supabase } from '@/lib/supabase';
import pdf from 'pdf-parse';

// Real PDF processing that extracts actual content only
async function processPDF(file: File): Promise<{ content: string; topics: string[] }> {
  try {
    console.log(`📄 Processing PDF: ${file.name} (${file.size} bytes)`);
    
    // Convert File to ArrayBuffer, then to Buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    console.log(`🔄 Extracting text from PDF buffer (${buffer.length} bytes)`);
    
    // Extract text from PDF with options
    const data = await pdf(buffer, {
      // PDF parsing options
      max: 0, // No page limit
      version: 'v1.10.100' // Specify version for compatibility
    });
    
    let content = data.text;
    console.log(`📝 Raw extracted text length: ${content.length} characters`);
    
    // Clean up the extracted text
    content = content
      .replace(/\s+/g, ' ') // Replace multiple whitespace with single space
      .replace(/\n\s*\n/g, '\n\n') // Clean up line breaks
      .replace(/[^\x20-\x7E\n\r\t]/g, '') // Remove non-printable characters except newlines and tabs
      .trim();
    
    console.log(`✨ Cleaned text length: ${content.length} characters`);
    
    if (!content || content.length < 50) {
      console.warn(`⚠️ PDF content too short: ${content.length} characters`);
      throw new Error('PDF appears to be empty or contains no extractable text. This might be a scanned PDF or image-based document that requires OCR processing.');
    }
    
    // Extract topics from actual content using keyword analysis
    const topics = extractTopicsFromContent(content, file.name);
    
    console.log(`🏷️ Extracted ${topics.length} topics from actual content:`, topics.slice(0, 5));
    
    return { content, topics };
  } catch (error) {
    console.error('❌ PDF processing error:', error);
    
    // Provide specific error messages without fallback content
    if (error instanceof Error) {
      if (error.message.includes('Invalid PDF')) {
        throw new Error('The uploaded file is not a valid PDF document. Please check the file and try again.');
      }
      if (error.message.includes('Password')) {
        throw new Error('This PDF is password protected. Please upload an unprotected PDF file.');
      }
      if (error.message.includes('Corrupt')) {
        throw new Error('The PDF file appears to be corrupted. Please try uploading a different file.');
      }
      if (error.message.includes('empty') || error.message.includes('no extractable text')) {
        throw new Error('This PDF contains no extractable text. It may be a scanned document or image-based PDF. Please upload a text-based PDF or use OCR software to convert it first.');
      }
    }
    
    // No fallback content - throw the original error
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    throw new Error(`Failed to process PDF "${file.name}": ${errorMessage}. Please ensure the file is a valid, unprotected PDF with extractable text.`);
  }
}

// Extract topics from actual content using keyword analysis
function extractTopicsFromContent(content: string, filename: string = ''): string[] {
  const text = content.toLowerCase();
  const topics: string[] = [];
  
  // Extract meaningful words and phrases from actual content
  const words = text.split(/\s+/).filter(word => 
    word.length > 3 && 
    word.length < 25 &&
    !/^\d+$/.test(word) && // Not just numbers
    /^[a-zA-Z]+$/.test(word) // Only letters
  );
  
  // Count word frequency
  const wordFreq = new Map<string, number>();
  words.forEach(word => {
    const normalized = word.toLowerCase();
    wordFreq.set(normalized, (wordFreq.get(normalized) || 0) + 1);
  });
  
  // Get most frequent meaningful words as topics
  const frequentWords = Array.from(wordFreq.entries())
    .filter(([word, count]) => count >= 2) // Appears at least twice
    .sort((a, b) => b[1] - a[1]) // Sort by frequency
    .slice(0, 15) // Take top 15
    .map(([word]) => word);
  
  topics.push(...frequentWords);
  
  // Extract proper nouns and capitalized terms from original content
  const properNouns = content.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g) || [];
  const uniqueProperNouns = [...new Set(properNouns.map(noun => noun.toLowerCase()))]
    .filter(noun => noun.length > 3 && noun.length < 25)
    .slice(0, 10);
  
  topics.push(...uniqueProperNouns);
  
  // Extract acronyms from actual content
  const acronyms = content.match(/\b[A-Z]{2,6}\b/g) || [];
  const uniqueAcronyms = [...new Set(acronyms.map(acronym => acronym.toLowerCase()))]
    .filter(acronym => acronym.length >= 2 && acronym.length <= 6)
    .slice(0, 5);
  
  topics.push(...uniqueAcronyms);
  
  // Extract key phrases using simple patterns
  const keyPhrases = [
    ...content.match(/\b\w+\s+\w+\s+\w+\b/g) || [], // 3-word phrases
    ...content.match(/\b\w+\s+\w+\b/g) || [] // 2-word phrases
  ];
  
  const meaningfulPhrases = [...new Set(keyPhrases)]
    .map(phrase => phrase.toLowerCase().trim())
    .filter(phrase => 
      phrase.length > 6 && 
      phrase.length < 30 &&
      !phrase.includes('the ') &&
      !phrase.includes('and ') &&
      !phrase.includes('for ') &&
      !phrase.includes('with ')
    )
    .slice(0, 10);
  
  topics.push(...meaningfulPhrases);
  
  // Remove duplicates and filter out generic terms
  const genericTerms = new Set([
    'the', 'and', 'for', 'are', 'with', 'this', 'that', 'from', 'they', 'have', 
    'will', 'been', 'were', 'said', 'each', 'which', 'their', 'time', 'would', 
    'there', 'could', 'other', 'more', 'very', 'what', 'know', 'just', 'first', 
    'into', 'over', 'think', 'also', 'your', 'work', 'life', 'only', 'can', 
    'still', 'should', 'after', 'being', 'now', 'made', 'before', 'here', 
    'through', 'when', 'where', 'much', 'some', 'these', 'many', 'then', 
    'them', 'well', 'were', 'document', 'content', 'text', 'data', 'system',
    'website', 'page', 'section', 'part', 'item', 'list', 'number', 'name'
  ]);
  
  const finalTopics = [...new Set(topics)]
    .filter(topic => 
      !genericTerms.has(topic) && 
      topic.length > 2 && 
      topic.length < 50 &&
      !/^\d+$/.test(topic)
    )
    .slice(0, 20);
  
  console.log(`🏷️ Final topics extracted from content: ${finalTopics.length} topics`);
  
  return finalTopics.length > 0 ? finalTopics : ['document'];
}

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Starting PDF upload process...');

    // Verify Supabase configuration
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
      console.error('❌ Missing NEXT_PUBLIC_SUPABASE_URL');
      return NextResponse.json(
        { error: 'Server configuration error: Missing Supabase URL' },
        { status: 500 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const sourceUrl = formData.get('sourceUrl') as string;
    const accessToken = formData.get('accessToken') as string;

    if (!accessToken) {
      console.error('❌ No access token provided in FormData');
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    // Verify the token and get the user
    console.log('🔍 Checking user authentication...');
    const { data: { user }, error: authError } = await supabase.auth.getUser(accessToken);

    if (authError || !user) {
      console.error('❌ Authentication error:', authError?.message || 'No user found');
      return NextResponse.json(
        { error: 'Unauthorized: Please sign in to upload files' },
        { status: 401 }
      );
    }

    console.log('✅ User authenticated:', user.email);
    
    if (!file) {
      console.error('❌ No file provided');
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }
    
    console.log('📁 File details:', {
      name: file.name,
      type: file.type,
      size: file.size
    });
    
    if (file.type !== 'application/pdf') {
      console.error('❌ Invalid file type:', file.type);
      return NextResponse.json(
        { error: 'Only PDF files are supported' },
        { status: 400 }
      );
    }
    
    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      console.error('❌ File too large:', file.size);
      return NextResponse.json(
        { error: 'File size must be less than 10MB' },
        { status: 400 }
      );
    }
    
    // Process the PDF with real content extraction only
    console.log('🔄 Processing PDF...');
    const { content, topics } = await processPDF(file);
    
    console.log('✅ PDF processed successfully');
    console.log('📊 Content length:', content.length);
    console.log('🏷️ Topics found:', topics.length);
    
    // Add to Supabase vector store with chunking for large documents
    console.log('💾 Storing in vector database...');
    const vectorStore = new SupabaseVectorStore(user.id);
    await vectorStore.addDocumentWithChunking({
      id: `pdf_${Date.now()}`,
      content,
      metadata: {
        title: file.name,
        source: file.name,
        sourceUrl: sourceUrl || undefined,
        type: 'pdf',
        topics,
        addedAt: new Date().toISOString()
      }
    }, user.id);
    
    const stats = await vectorStore.getStats(user.id);
    
    console.log('✅ PDF upload completed successfully');
    
    console.log('✅ PDF upload completed successfully');
    
    return NextResponse.json({
      success: true,
      content: content.substring(0, 500) + (content.length > 500 ? '...' : ''), // Return preview
      topics,
      filename: file.name,
      size: file.size,
      embeddingType: stats.embeddingType
    });
    
  } catch (error) {
    console.error('❌ PDF upload error:', error);
    
    // Enhanced error logging with complete error details
    let errorMessage = 'Failed to process PDF file';
    let statusCode = 500;
    
    if (error instanceof Error) {
      // Log complete error details including all properties
      const errorDetails = {
        name: error.name,
        message: error.message,
        stack: error.stack,
        // Include any additional error properties
        ...(error as any)
      };
      
      console.error('Complete error details:', JSON.stringify(errorDetails, null, 2));
      
      // Log Supabase URL (masking sensitive parts)
      if (process.env.NEXT_PUBLIC_SUPABASE_URL) {
        const url = new URL(process.env.NEXT_PUBLIC_SUPABASE_URL);
        console.log('Supabase host:', url.host);
      } else {
        console.error('NEXT_PUBLIC_SUPABASE_URL is not set');
      }
      
      errorMessage = error.message;
      
      // Handle specific error types
      if (error.name === 'AuthError') {
        statusCode = 401;
      } else if (errorMessage.includes('permission') || errorMessage.includes('unauthorized')) {
        statusCode = 403;
      } else if (errorMessage.includes('storage') || errorMessage.includes('bucket')) {
        errorMessage = 'Storage configuration error. Please check if the storage bucket is properly configured.';
      }
    }
    
    return NextResponse.json(
      { 
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? error?.toString() : undefined
      },
      { status: statusCode }
    );
  }
}