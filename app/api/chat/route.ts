import { NextRequest, NextResponse } from 'next/server';
import { OpenRouterClient } from '@/lib/openrouter';
import { enhancedVectorStore } from '@/lib/enhanced-vector-store';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

interface KnowledgeItem {
  id: string;
  title: string;
  type: 'pdf' | 'url';
  source: string;
  sourceUrl?: string;
  content: string;
  addedAt: string;
  status: 'active' | 'processing' | 'error';
  topics: string[];
}

// Initialize OpenRouter client
const getOpenRouterClient = () => {
  const apiKey = process.env.OPENROUTER_API_KEY;
  if (!apiKey) {
    throw new Error('OPENROUTER_API_KEY environment variable is not set');
  }
  return new OpenRouterClient(apiKey);
};

// Load knowledge base from request headers (passed from client)
const loadKnowledgeBaseFromClient = async (knowledgeBaseData: string) => {
  try {
    // Always start fresh - clear any existing data
    enhancedVectorStore.clear();
    
    if (knowledgeBaseData && knowledgeBaseData !== 'null') {
      const items = JSON.parse(knowledgeBaseData);
      
      console.log(`📚 Loading ${items.length} documents into vector store...`);
      
      // Add documents to enhanced vector store
      for (const item of items) {
        if (item.status === 'active') {
          await enhancedVectorStore.addDocumentWithChunking({
            id: item.id,
            content: item.content,
            metadata: {
              title: item.title,
              source: item.source,
              sourceUrl: item.sourceUrl,
              type: item.type,
              topics: item.topics || [],
              addedAt: item.addedAt
            }
          });
        }
      }
      
      console.log(`✅ Loaded ${items.length} documents into vector store`);
      
      // Debug the knowledge base
      enhancedVectorStore.debugKnowledgeBase();
    }
  } catch (error) {
    console.error('Error loading knowledge base:', error);
    // If there's an error, ensure we start with a clean slate
    enhancedVectorStore.clear();
  }
};

export async function POST(request: NextRequest) {
  try {
    const { message, sessionId, knowledgeBase } = await request.json();
    
    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Invalid message format' },
        { status: 400 }
      );
    }

    console.log(`🔍 Processing query: "${message}"`);

    // Load knowledge base from client data
    await loadKnowledgeBaseFromClient(knowledgeBase);

    // Get all documents to check what's available
    const allDocuments = enhancedVectorStore.getAllDocuments();
    
    if (allDocuments.length === 0) {
      return NextResponse.json({
        response: "I apologize, but no knowledge base sources have been added yet. Please ask an administrator to upload documents or add URLs to the knowledge base through the admin panel at /admin.",
        fromKnowledgeBase: false,
        sessionId
      });
    }

    // Debug the search with the specific query
    enhancedVectorStore.debugKnowledgeBase(message);

    // Step 1: Perform a broad search to ensure we capture potential matches
    const initialSearchResults = await enhancedVectorStore.searchWithKeywords(message, 5, 0.6);

    // Step 2: Filter results based on a stricter confidence score
    const MIN_CONFIDENCE_SCORE = 0.8;
    const confidentResults = initialSearchResults.filter(result => result.score >= MIN_CONFIDENCE_SCORE);

    if (confidentResults.length === 0) {
      const availableTopics = [...new Set(allDocuments.flatMap(doc => doc.metadata.topics))].slice(0, 15);
      const availableSources = [...new Set(allDocuments.map(doc => doc.metadata.title))];

      console.log(`❌ No confident search results for: "${message}" (top score: ${initialSearchResults.length > 0 ? initialSearchResults[0].score.toFixed(3) : 'N/A'})`);

      let guidance = "I couldn't find any information related to your question in my knowledge base.";
      if (availableSources.length > 0) {
        guidance += `\n\nI can answer questions about the following topics:\n${availableTopics.map(t => `• ${t}`).join('\n')}`;
      }

      return NextResponse.json({
        response: guidance,
        fromKnowledgeBase: false,
        sessionId,
        availableSources,
        availableTopics
      });
    }

    console.log(`✅ Found ${confidentResults.length} relevant documents`);

    // Combine relevant context from search results
    const context = confidentResults
      .map(result => `Source: ${result.document.metadata.title} (Match: ${result.matchType}, Score: ${result.score.toFixed(3)})\n${result.document.content}`)
      .join('\n\n---\n\n');

    // Deduplicate source names and create source URL mapping
    const uniqueSources = [...new Set(confidentResults.map(result => result.document.metadata.title))];
    const sources = uniqueSources.join(', ');

    // Create mapping of source titles to URLs
    const sourceUrls: { [title: string]: string } = {};
    confidentResults.forEach(result => {
      const title = result.document.metadata.title;
      const sourceUrl = result.document.metadata.sourceUrl;
      if (sourceUrl && !sourceUrls[title]) {
        sourceUrls[title] = sourceUrl;
      }
    });

    // Check if OpenRouter API key is available
    const openRouterApiKey = process.env.OPENROUTER_API_KEY;
    
    if (!openRouterApiKey) {
      // Fallback to simple response generation
      const bestMatch = confidentResults[0];
      const response = `Based on the information in my knowledge base:\n\n${bestMatch.document.content.substring(0, 600)}${bestMatch.document.content.length > 600 ? '...' : ''}\n\nSource: ${bestMatch.document.metadata.title}\nMatch Type: ${bestMatch.matchType}\nRelevance Score: ${(bestMatch.score * 100).toFixed(1)}%`;

      return NextResponse.json({
        response,
        fromKnowledgeBase: true,
        source: sources,
        sourceUrls: Object.keys(sourceUrls).length > 0 ? sourceUrls : undefined,
        sessionId,
        searchResults: confidentResults.length,
        embeddingType: enhancedVectorStore.getStats().embeddingType
      });
    }

    // Use OpenRouter for intelligent response generation
    try {
      const openRouter = getOpenRouterClient();
      const aiResponse = await openRouter.generateResponse(message, context);

      return NextResponse.json({
        response: aiResponse,
        fromKnowledgeBase: true,
        source: sources,
        sourceUrls: Object.keys(sourceUrls).length > 0 ? sourceUrls : undefined,
        sessionId,
        searchResults: confidentResults.length,
        searchScores: confidentResults.map(r => (r.score * 100).toFixed(1)),
        embeddingType: enhancedVectorStore.getStats().embeddingType
      });
    } catch (aiError) {
      console.error('OpenRouter API error:', aiError);
      
      // Fallback response with better formatting
      const bestMatch = confidentResults[0];
      const fallbackResponse = `Based on the information in my knowledge base:\n\n${bestMatch.document.content.substring(0, 800)}${bestMatch.document.content.length > 800 ? '...' : ''}\n\nSource: ${bestMatch.document.metadata.title}\nMatch Type: ${bestMatch.matchType}\n\nNote: AI enhancement temporarily unavailable, showing direct knowledge base content.`;

      return NextResponse.json({
        response: fallbackResponse,
        fromKnowledgeBase: true,
        source: sources,
        sourceUrls: Object.keys(sourceUrls).length > 0 ? sourceUrls : undefined,
        sessionId,
        searchResults: confidentResults.length,
        embeddingType: enhancedVectorStore.getStats().embeddingType
      });
    }
    
  } catch (error) {
    console.error('Chat API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}