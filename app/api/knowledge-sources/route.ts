import { NextRequest, NextResponse } from 'next/server';
import { KnowledgeSourcesDB } from '@/lib/database';
import { supabase } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    // Get the auth token from the Authorization header
    const authHeader = request.headers.get('Authorization');
    const token = authHeader?.split(' ')[1];

    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const sources = await KnowledgeSourcesDB.getAll(user.id);
    return NextResponse.json({ sources });
  } catch (error) {
    console.error('Error fetching knowledge sources:', error);
    return NextResponse.json({ error: 'Failed to fetch knowledge sources' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the auth token from the Authorization header
    const authHeader = request.headers.get('Authorization');
    const token = authHeader?.split(' ')[1];

    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const knowledgeItem = await request.json();
    const created = await KnowledgeSourcesDB.create(knowledgeItem, user.id);
    
    return NextResponse.json({ source: created });
  } catch (error) {
    console.error('Error creating knowledge source:', error);
    return NextResponse.json({ error: 'Failed to create knowledge source' }, { status: 500 });
  }
}