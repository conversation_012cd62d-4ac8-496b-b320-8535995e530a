import { NextRequest, NextResponse } from 'next/server';
import { KnowledgeSourcesDB } from '@/lib/database';
import { supabase } from '@/lib/supabase';
import { SupabaseVectorStore } from '@/lib/supabase-vector-store';

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  try {
    // Get the auth token from the Authorization header
    const authHeader = request.headers.get('Authorization');
    const token = authHeader?.split(' ')[1];

    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const updates = await request.json();
    const updated = await KnowledgeSourcesDB.update(id, updates, user.id);
    
    return NextResponse.json({ source: updated });
  } catch (error) {
    console.error('Error updating knowledge source:', error);
    return NextResponse.json({ error: 'Failed to update knowledge source' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  try {
    console.log(`🔐 DELETE request for knowledge source: ${id}`);

    // Get the auth token from the Authorization header
    const authHeader = request.headers.get('Authorization');
    const token = authHeader?.split(' ')[1];

    if (!token) {
      console.error('❌ No authorization token provided');
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      console.error('❌ Authentication failed:', authError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log(`👤 Authenticated user: ${user.id} attempting to delete: ${id}`);

    // Use the SupabaseVectorStore removeDocument method for proper deletion
    const vectorStore = new SupabaseVectorStore(user.id);
    await vectorStore.removeDocument(id, user.id);

    console.log(`✅ Successfully deleted knowledge source: ${id}`);
    return NextResponse.json({ success: true, message: 'Knowledge source deleted successfully' });
  } catch (error) {
    console.error('❌ Error deleting knowledge source:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    return NextResponse.json({
      error: 'Failed to delete knowledge source',
      details: errorMessage
    }, { status: 500 });
  }
}