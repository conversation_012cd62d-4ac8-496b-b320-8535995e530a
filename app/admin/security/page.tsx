'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, AlertTriangle, Activity, Users, FileText, Globe } from 'lucide-react';

interface SecurityMetrics {
  rateLimitViolations: number;
  suspiciousActivities: number;
  blockedRequests: number;
  activeUsers: number;
  totalRequests: number;
  errorRate: number;
}

interface SecurityEvent {
  id: string;
  type: 'rate_limit' | 'auth_failure' | 'suspicious_activity' | 'error';
  ip: string;
  timestamp: string;
  endpoint?: string;
  details?: any;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export default function SecurityDashboard() {
  const [metrics, setMetrics] = useState<SecurityMetrics>({
    rateLimitViolations: 0,
    suspiciousActivities: 0,
    blockedRequests: 0,
    activeUsers: 0,
    totalRequests: 0,
    errorRate: 0
  });

  const [recentEvents, setRecentEvents] = useState<SecurityEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchSecurityMetrics();
    fetchRecentEvents();
    
    // Refresh every 30 seconds
    const interval = setInterval(() => {
      fetchSecurityMetrics();
      fetchRecentEvents();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const fetchSecurityMetrics = async () => {
    try {
      // TODO: Implement actual API call
      // const response = await fetch('/api/admin/security/metrics');
      // const data = await response.json();
      
      // Mock data for now
      setMetrics({
        rateLimitViolations: Math.floor(Math.random() * 50),
        suspiciousActivities: Math.floor(Math.random() * 10),
        blockedRequests: Math.floor(Math.random() * 100),
        activeUsers: Math.floor(Math.random() * 500),
        totalRequests: Math.floor(Math.random() * 10000),
        errorRate: Math.random() * 5
      });
    } catch (error) {
      console.error('Failed to fetch security metrics:', error);
    }
  };

  const fetchRecentEvents = async () => {
    try {
      // TODO: Implement actual API call
      // const response = await fetch('/api/admin/security/events');
      // const data = await response.json();
      
      // Mock data for now
      const mockEvents: SecurityEvent[] = [
        {
          id: '1',
          type: 'rate_limit',
          ip: '*************',
          timestamp: new Date().toISOString(),
          endpoint: '/api/chat-public',
          severity: 'medium'
        },
        {
          id: '2',
          type: 'suspicious_activity',
          ip: '*********',
          timestamp: new Date(Date.now() - 300000).toISOString(),
          endpoint: '/api/upload-pdf',
          severity: 'high'
        }
      ];
      
      setRecentEvents(mockEvents);
      setIsLoading(false);
    } catch (error) {
      console.error('Failed to fetch security events:', error);
      setIsLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'default';
    }
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'rate_limit': return <Activity className="h-4 w-4" />;
      case 'auth_failure': return <Users className="h-4 w-4" />;
      case 'suspicious_activity': return <AlertTriangle className="h-4 w-4" />;
      case 'error': return <FileText className="h-4 w-4" />;
      default: return <Shield className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading security dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Security Dashboard</h1>
          <p className="text-gray-600">Monitor security events and system health</p>
        </div>
        <Button onClick={() => { fetchSecurityMetrics(); fetchRecentEvents(); }}>
          Refresh
        </Button>
      </div>

      {/* Security Alerts */}
      {metrics.rateLimitViolations > 20 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            High number of rate limit violations detected ({metrics.rateLimitViolations} in the last hour).
            Consider reviewing rate limiting policies.
          </AlertDescription>
        </Alert>
      )}

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rate Limit Violations</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.rateLimitViolations}</div>
            <p className="text-xs text-muted-foreground">Last 24 hours</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Suspicious Activities</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.suspiciousActivities}</div>
            <p className="text-xs text-muted-foreground">Requires investigation</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Blocked Requests</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.blockedRequests}</div>
            <p className="text-xs text-muted-foreground">Automatically blocked</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.activeUsers}</div>
            <p className="text-xs text-muted-foreground">Currently online</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalRequests.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Last 24 hours</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.errorRate.toFixed(2)}%</div>
            <p className="text-xs text-muted-foreground">System health</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Security Events */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Security Events</CardTitle>
          <CardDescription>Latest security incidents and violations</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentEvents.length === 0 ? (
              <p className="text-center text-gray-500 py-4">No recent security events</p>
            ) : (
              recentEvents.map((event) => (
                <div key={event.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getEventIcon(event.type)}
                    <div>
                      <p className="font-medium">{event.type.replace('_', ' ').toUpperCase()}</p>
                      <p className="text-sm text-gray-600">
                        IP: {event.ip} {event.endpoint && `• ${event.endpoint}`}
                      </p>
                      <p className="text-xs text-gray-500">
                        {new Date(event.timestamp).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  <Badge variant={getSeverityColor(event.severity) as any}>
                    {event.severity}
                  </Badge>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
