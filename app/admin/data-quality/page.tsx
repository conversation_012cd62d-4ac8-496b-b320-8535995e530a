'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, CheckCircle, Trash2, Search, RefreshCw } from 'lucide-react';

interface ValidationResults {
  totalEmbeddings: number;
  invalidSourceIds: number;
  orphanedEmbeddings: number;
  validEmbeddings: number;
  issues: string[];
}

interface CleanupResults {
  deletedCount: number;
  invalidSourceIds: string[];
}

export default function DataQualityPage() {
  const [validation, setValidation] = useState<ValidationResults | null>(null);
  const [cleanup, setCleanup] = useState<CleanupResults | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const runValidation = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/admin/data-quality?action=validate');
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to validate data');
      }
      
      setValidation(data.validation);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const runCleanup = async () => {
    if (!confirm('Are you sure you want to delete all embeddings with invalid UUIDs? This action cannot be undone.')) {
      return;
    }

    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/admin/data-quality', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'cleanup' })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to cleanup data');
      }
      
      setCleanup(data.cleanup);
      // Refresh validation after cleanup
      await runValidation();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Data Quality Management</h1>
        <p className="text-muted-foreground mt-2">
          Monitor and maintain the integrity of your knowledge base embeddings
        </p>
      </div>

      {error && (
        <Card className="mb-6 border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-6">
        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Actions</CardTitle>
            <CardDescription>
              Validate data integrity and clean up invalid embeddings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <Button 
                onClick={runValidation} 
                disabled={loading}
                variant="outline"
              >
                <Search className="h-4 w-4 mr-2" />
                {loading ? 'Validating...' : 'Validate Data'}
              </Button>
              
              <Button 
                onClick={runCleanup} 
                disabled={loading || !validation || validation.invalidSourceIds === 0}
                variant="destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {loading ? 'Cleaning...' : 'Clean Up Invalid Data'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Validation Results */}
        {validation && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Validation Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{validation.totalEmbeddings}</div>
                  <div className="text-sm text-muted-foreground">Total Embeddings</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{validation.validEmbeddings}</div>
                  <div className="text-sm text-muted-foreground">Valid Embeddings</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{validation.invalidSourceIds}</div>
                  <div className="text-sm text-muted-foreground">Invalid UUIDs</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{validation.orphanedEmbeddings}</div>
                  <div className="text-sm text-muted-foreground">Orphaned</div>
                </div>
              </div>
              
              {validation.issues.length > 0 && (
                <div>
                  <h4 className="font-semibold mb-2">Issues Found:</h4>
                  <div className="space-y-2">
                    {validation.issues.map((issue, index) => (
                      <Badge key={index} variant="destructive">
                        {issue}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              
              {validation.issues.length === 0 && (
                <div className="flex items-center gap-2 text-green-700">
                  <CheckCircle className="h-5 w-5" />
                  <span>No data quality issues found!</span>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Cleanup Results */}
        {cleanup && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <RefreshCw className="h-5 w-5 text-blue-600" />
                Cleanup Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="text-lg font-semibold">
                    Deleted {cleanup.deletedCount} invalid embeddings
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Removed embeddings with {cleanup.invalidSourceIds.length} unique invalid source IDs
                  </div>
                </div>
                
                {cleanup.invalidSourceIds.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-2">Removed Invalid Source IDs:</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {cleanup.invalidSourceIds.slice(0, 10).map((id, index) => (
                        <code key={index} className="text-xs bg-gray-100 p-1 rounded">
                          {id}
                        </code>
                      ))}
                      {cleanup.invalidSourceIds.length > 10 && (
                        <div className="text-sm text-muted-foreground">
                          ... and {cleanup.invalidSourceIds.length - 10} more
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>How to Use</CardTitle>
          </CardHeader>
          <CardContent>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>Click "Validate Data" to check for data quality issues</li>
              <li>Review the validation results to understand any problems</li>
              <li>If invalid UUIDs are found, click "Clean Up Invalid Data" to remove them</li>
              <li>Run validation again to confirm the cleanup was successful</li>
            </ol>
            
            <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div className="text-sm">
                  <strong>Warning:</strong> The cleanup operation permanently deletes embeddings with invalid UUIDs. 
                  This action cannot be undone. Make sure to backup your data if needed.
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
