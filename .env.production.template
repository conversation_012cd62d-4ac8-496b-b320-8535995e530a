# 🔒 PRODUCTION ENVIRONMENT VARIABLES TEMPLATE
# Copy this file and configure with your actual production values
# NEVER commit the actual production .env file to version control

# ⚠️ CRITICAL: Replace all placeholder values with actual production credentials

# Required: OpenRouter API Key for AI responses
OPENROUTER_API_KEY=your_actual_openrouter_api_key_here

# Optional: Default AI model for OpenRouter
OPENROUTER_DEFAULT_MODEL=deepseek/deepseek-chat-v3-0324:free

# Optional: Google Gemini API Key for enhanced embeddings
GEMINI_API_KEY=your_actual_gemini_api_key_here
NEXT_PUBLIC_GEMINI_API_KEY=your_actual_gemini_api_key_here

# Required: Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_actual_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_actual_supabase_service_role_key

# Required: Rate Limiting (Upstash Redis)
# Sign up at https://upstash.com and create a Redis database
UPSTASH_REDIS_REST_URL=https://your-redis-instance.upstash.io
UPSTASH_REDIS_REST_TOKEN=your_actual_upstash_redis_token

# Required: Site URL
NEXT_PUBLIC_SITE_URL=https://yourdomain.com

# Security Configuration
NODE_ENV=production

# Optional: Monitoring and Error Tracking
SENTRY_DSN=your_sentry_dsn_here
DATADOG_API_KEY=your_datadog_api_key_here

# Optional: Additional Security Headers
SECURITY_HEADERS_ENABLED=true
CSP_REPORT_URI=https://yourdomain.com/api/csp-report

# Optional: Admin Configuration
ADMIN_EMAIL_DOMAINS=yourdomain.com,anotherdomain.com
MAX_ADMIN_SESSIONS=5

# Optional: Performance Configuration
MAX_REQUEST_SIZE=10mb
REQUEST_TIMEOUT=30000
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# Optional: Feature Flags
ENABLE_FILE_UPLOADS=true
ENABLE_URL_PROCESSING=true
ENABLE_PUBLIC_CHAT=true
ENABLE_ANALYTICS=true
