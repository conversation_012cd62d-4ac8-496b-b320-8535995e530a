import { supabase } from './supabase';
import { v4 as uuidv4 } from 'uuid';

export interface SearchHistoryEntry {
  id: string;
  query: string;
  timestamp: string;
  user_id?: string;
  session_id?: string;
}

// Generate a session ID for anonymous users
function getSessionId(): string {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
    let sessionId = localStorage.getItem('search_session_id');
    if (!sessionId) {
      sessionId = uuidv4(); // Generate a proper UUID
      localStorage.setItem('search_session_id', sessionId);
    }
    return sessionId;
  } else {
    // Server-side: generate a proper UUID for temporary session
    return uuidv4();
  }
}

// Save a search query to the database
export async function saveSearchQuery(query: string, userId?: string): Promise<void> {
  try {
    // Don't save empty queries or system queries
    if (!query || query.trim().length === 0 || query.startsWith('__')) {
      return;
    }

    const trimmedQuery = query.trim().toLowerCase();

    // Don't save very short queries (less than 2 characters)
    if (trimmedQuery.length < 2) {
      return;
    }

    // For anonymous users, don't use session_id since it requires a foreign key
    // Instead, we'll rely on user_id being null to identify anonymous searches
    const sessionId = userId ? null : null; // Set to null for anonymous users

    // Insert the search query into chat_messages table
    // We'll use this table to store search history alongside chat messages
    const { error } = await supabase
      .from('chat_messages')
      .insert({
        content: trimmedQuery,  // Fixed: use 'content' instead of 'message'
        role: 'user',           // Fixed: use 'role' instead of 'sender'
        user_id: userId || null,
        session_id: sessionId,  // Always null for now to avoid foreign key issues
        created_at: new Date().toISOString(),
        // Mark this as a search query for easy filtering
        metadata: { type: 'search_query' }
      });

    if (error) {
      console.error('Error saving search query:', {
        error: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        query: trimmedQuery,
        userId,
        sessionId
      });
    } else {
      console.log('✅ Search query saved successfully:', trimmedQuery);
    }
  } catch (error) {
    console.error('Error saving search query (catch block):', {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      query
    });
  }
}

// Get recent search queries for suggestions
export async function getRecentSearchQueries(limit: number = 10): Promise<string[]> {
  try {
    // Get recent search queries from all users (last 24 hours)
    // Since we're not using session_id for anonymous users, we'll get general recent searches
    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);

    // Get recent search queries from all users (last 24 hours)
    const { data, error } = await supabase
      .from('chat_messages')
      .select('content, created_at, metadata')
      .eq('role', 'user')
      .not('metadata', 'is', null)
      .gte('created_at', oneDayAgo.toISOString())
      .order('created_at', { ascending: false })
      .limit(limit * 2); // Get more to filter duplicates

    if (error) {
      console.error('Error fetching recent searches:', {
        error: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      });
      return [];
    }

    if (!data || data.length === 0) {
      console.log('No recent search queries found');
      return [];
    }

    // Filter for search queries in JavaScript
    const searchQueries = data.filter(item => {
      return item.metadata &&
             typeof item.metadata === 'object' &&
             item.metadata.type === 'search_query';
    });

    // Remove duplicates and filter out very short queries
    const uniqueQueries = Array.from(new Set(
      searchQueries
        .map(item => item.content)
        .filter(query => query && query.length >= 2)
    )).slice(0, limit);

    return uniqueQueries;
  } catch (error) {
    console.error('Error fetching recent searches (catch block):', {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined
    });
    return [];
  }
}

// Get popular search queries across all users
export async function getPopularSearchQueries(limit: number = 8): Promise<string[]> {
  try {
    // Get popular search queries from all users (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const { data, error } = await supabase
      .from('chat_messages')
      .select('content, metadata')
      .eq('role', 'user')
      .not('metadata', 'is', null)
      .gte('created_at', thirtyDaysAgo.toISOString())
      .order('created_at', { ascending: false })
      .limit(200); // Get a larger sample to analyze

    if (error) {
      console.error('Error fetching popular searches:', {
        error: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      });
      return getDefaultSuggestions();
    }

    if (!data || data.length === 0) {
      console.log('No popular search queries found, returning defaults');
      return getDefaultSuggestions();
    }

    // Filter for search queries in JavaScript
    const searchQueries = data.filter(item => {
      return item.metadata &&
             typeof item.metadata === 'object' &&
             item.metadata.type === 'search_query';
    });

    if (searchQueries.length === 0) {
      return getDefaultSuggestions();
    }

    // Count frequency of each query
    const queryCount: { [key: string]: number } = {};
    searchQueries.forEach(item => {
      const query = item.content?.toLowerCase().trim();
      if (query && query.length >= 2) {
        queryCount[query] = (queryCount[query] || 0) + 1;
      }
    });

    // Sort by frequency and return top queries
    const popularQueries = Object.entries(queryCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit)
      .map(([query]) => query);

    // If we don't have enough popular queries, supplement with defaults
    if (popularQueries.length < limit) {
      const defaults = getDefaultSuggestions();
      const combined = [...popularQueries];
      
      for (const defaultQuery of defaults) {
        if (!combined.includes(defaultQuery.toLowerCase()) && combined.length < limit) {
          combined.push(defaultQuery.toLowerCase());
        }
      }
      
      return combined.slice(0, limit);
    }

    return popularQueries;
  } catch (error) {
    console.error('Error fetching popular searches (catch block):', {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined
    });
    return getDefaultSuggestions();
  }
}

// Default search suggestions when no history is available
function getDefaultSuggestions(): string[] {
  return [
    'cara daftar LPDP',
    'syarat beasiswa LPDP',
    'jadwal pendaftaran',
    'dokumen yang diperlukan',
    'universitas tujuan',
    'mata garuda',
    'beasiswa reguler',
    'beasiswa afirmasi'
  ];
}

// Clean up old search history (optional maintenance function)
export async function cleanupOldSearchHistory(daysToKeep: number = 90): Promise<void> {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const { error } = await supabase
      .from('chat_messages')
      .delete()
      .eq('metadata->>type', 'search_query') // Fixed: use ->> for text extraction
      .lt('created_at', cutoffDate.toISOString());

    if (error) {
      console.error('Error cleaning up old search history:', {
        error: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      });
    } else {
      console.log(`✅ Cleaned up search history older than ${daysToKeep} days`);
    }
  } catch (error) {
    console.error('Error cleaning up old search history (catch block):', {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined
    });
  }
}
