import { KnowledgeSourcesDB, DocumentEmbeddingsDB } from './database'
import { GeminiEmbeddingClient } from './gemini-embeddings'
import { KnowledgeItem } from './knowledge-base-types'

interface VectorDocument {
  id: string;
  content: string;
  metadata: {
    title: string;
    source: string;
    sourceUrl?: string;
    type: 'pdf' | 'url';
    topics: string[];
    addedAt: string;
    chunkIndex?: number;
    totalChunks?: number;
    fileSize?: number;
    fileName?: string;
  };
  embedding?: number[];
}

interface SearchResult {
  document: VectorDocument;
  score: number;
  matchType: 'vector' | 'topic' | 'keyword' | 'combined';
}

export class SupabaseVectorStore {
  private geminiClient: GeminiEmbeddingClient | null = null;
  private useGeminiEmbeddings: boolean = false;
  private userId: string | null = null;
  private accessToken: string | null = null;

  constructor(userId?: string, accessToken?: string) {
    this.userId = userId || null;
    this.accessToken = accessToken || null;
    this.initializeGeminiClient();
  }

  private initializeGeminiClient() {
    const geminiApiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY || 
                         (typeof window !== 'undefined' ? 
                           localStorage.getItem('gemini_api_key') : null);
    
    if (geminiApiKey) {
      this.geminiClient = new GeminiEmbeddingClient(geminiApiKey);
      this.useGeminiEmbeddings = true;
      console.log('✅ Gemini text-embedding-004 enabled for Supabase');
    } else {
      console.log('⚠️ Gemini API key not found, using simple embeddings');
    }
  }

  // Extract meaningful topics from content with enhanced quality filtering
  private extractTopicsFromContent(content: string, title: string = ''): string[] {
    const text = `${title} ${content}`.toLowerCase();
    const originalText = `${title} ${content}`;
    const candidateTopics = new Map<string, { frequency: number; score: number; sources: Set<string> }>();

    // Enhanced generic terms filtering with more comprehensive blacklist
    const genericTerms = new Set([
      'the', 'and', 'for', 'are', 'with', 'this', 'that', 'from', 'they', 'have',
      'will', 'been', 'were', 'said', 'each', 'which', 'their', 'time', 'would',
      'there', 'could', 'other', 'more', 'very', 'what', 'know', 'just', 'first',
      'into', 'over', 'think', 'also', 'your', 'work', 'life', 'only', 'can',
      'still', 'should', 'after', 'being', 'now', 'made', 'before', 'here',
      'through', 'when', 'where', 'much', 'some', 'these', 'many', 'then',
      'them', 'well', 'were', 'information', 'content', 'text', 'data', 'system',
      'website', 'page', 'section', 'part', 'item', 'list', 'number', 'name',
      // Additional generic terms for better filtering
      'additional', 'various', 'multiple', 'several', 'different', 'including',
      'related', 'following', 'based', 'using', 'such', 'example', 'examples',
      'important', 'available', 'possible', 'general', 'specific', 'common',
      'basic', 'main', 'primary', 'secondary', 'current', 'new', 'old', 'good',
      'best', 'better', 'great', 'small', 'large', 'high', 'low', 'long', 'short'
    ]);

    // Helper function to add topic with scoring
    const addTopic = (topic: string, source: string, baseScore: number = 1) => {
      if (topic.length < 4 || topic.length > 30 || genericTerms.has(topic) || /^\d+$/.test(topic)) {
        return;
      }

      if (!candidateTopics.has(topic)) {
        candidateTopics.set(topic, { frequency: 0, score: 0, sources: new Set() });
      }

      const topicData = candidateTopics.get(topic)!;
      topicData.frequency += 1;
      topicData.sources.add(source);

      // Quality scoring: favor compound terms and title presence
      let qualityScore = baseScore;
      if (topic.includes(' ')) qualityScore *= 1.5; // Compound terms bonus
      if (title.toLowerCase().includes(topic)) qualityScore *= 1.3; // Title presence bonus

      topicData.score += qualityScore;
    };

    // Extract proper nouns and capitalized terms with frequency tracking
    const properNouns = originalText.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g) || [];
    properNouns.forEach(noun => {
      const normalized = noun.toLowerCase();
      addTopic(normalized, 'proper_noun', 2);
    });

    // Extract acronyms with frequency tracking
    const acronyms = originalText.match(/\b[A-Z]{2,10}\b/g) || [];
    acronyms.forEach(acronym => {
      if (acronym.length >= 2 && acronym.length <= 6) {
        addTopic(acronym.toLowerCase(), 'acronym', 1.5);
      }
    });

    // Extract meaningful compound words with frequency tracking
    const phrases = text.match(/\b\w+[-_]\w+(?:[-_]\w+)*\b/g) || [];
    phrases.forEach(phrase => {
      if (phrase.length > 4 && phrase.length < 25) {
        const normalized = phrase.replace(/[-_]/g, ' ');
        addTopic(normalized, 'compound', 1.8);
      }
    });

    // Extract important terms that appear frequently with enhanced filtering
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 20);

    sentences.forEach(sentence => {
      const words = sentence.split(/\s+/).filter(word =>
        word.length > 3 &&
        word.length < 20 &&
        !/^\d+$/.test(word) &&
        /^[a-zA-Z]+$/.test(word)
      );

      words.forEach(word => {
        addTopic(word, 'frequent_term', 1);
      });
    });

    // Filter and rank topics based on frequency and quality score
    const qualifiedTopics = Array.from(candidateTopics.entries())
      .filter(([topic, data]) =>
        data.frequency >= 2 && // Must appear at least 2 times
        !genericTerms.has(topic) &&
        topic.length >= 4 &&
        topic.length <= 30 &&
        !/^\d+$/.test(topic)
      )
      .sort((a, b) => {
        // Sort by combined score (frequency * quality score)
        const scoreA = a[1].frequency * a[1].score;
        const scoreB = b[1].frequency * b[1].score;
        return scoreB - scoreA;
      })
      .slice(0, 10) // Reduced from 20 to 10
      .map(([topic]) => topic);

    console.log(`📋 Extracted ${qualifiedTopics.length} high-quality topics from "${title}":`, qualifiedTopics);

    return qualifiedTopics;
  }

  // Simple text-to-vector conversion (fallback)
  private textToVector(text: string): number[] {
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2);
    
    const vector = new Array(768).fill(0); // Match Gemini dimensions
    
    const wordFreq = new Map<string, number>();
    words.forEach(word => {
      wordFreq.set(word, (wordFreq.get(word) || 0) + 1);
    });
    
    for (const [word, freq] of wordFreq.entries()) {
      for (let j = 0; j < word.length; j++) {
        const charCode = word.charCodeAt(j);
        const index = (charCode * (j + 1) + word.length) % vector.length;
        vector[index] += freq * Math.log(1 + freq);
      }
    }
    
    const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    return magnitude > 0 ? vector.map(val => val / magnitude) : vector;
  }

  async addDocument(document: Omit<VectorDocument, 'embedding'>, userId: string): Promise<void> {
    try {
      // Validate userId format
      if (!this.isValidUUID(userId)) {
        throw new Error(`Invalid UUID format for userId: ${userId}`);
      }

      // Extract topics from content
      const topics = this.extractTopicsFromContent(document.content, document.metadata.title);

      // Create knowledge source in database
      const knowledgeItem: Omit<KnowledgeItem, 'id' | 'addedAt'> = {
        title: document.metadata.title,
        type: document.metadata.type,
        source: document.metadata.source,
        sourceUrl: document.metadata.sourceUrl,
        content: document.content,
        topics,
        status: 'active'
      };

      const savedItem = await KnowledgeSourcesDB.create(knowledgeItem, userId, this.accessToken || undefined);

      // Validate the returned ID before proceeding
      if (!this.isValidUUID(savedItem.id)) {
        throw new Error(`Invalid UUID returned from database for knowledge source: ${savedItem.id}`);
      }

      // Generate and store embeddings
      await this.generateAndStoreEmbeddings(savedItem.id, document.content, topics);

      console.log(`✅ Added document to Supabase: ${document.metadata.title} (ID: ${savedItem.id})`);
    } catch (error) {
      console.error('Error adding document to Supabase:', error);
      throw error;
    }
  }

  async addDocumentWithChunking(document: Omit<VectorDocument, 'embedding'>, userId: string, maxChunkSize: number = 1500): Promise<void> {
    try {
      // Validate userId format
      if (!this.isValidUUID(userId)) {
        throw new Error(`Invalid UUID format for userId: ${userId}`);
      }

      if (!this.geminiClient || document.content.length <= maxChunkSize) {
        return this.addDocument(document, userId);
      }

      // Chunk the content
      const chunks = this.geminiClient.chunkText(document.content, maxChunkSize, 150);

      // Extract topics from full content
      const topics = this.extractTopicsFromContent(document.content, document.metadata.title);

      // Create knowledge source with the provided user ID
      const knowledgeItem: Omit<KnowledgeItem, 'id' | 'addedAt'> = {
        title: document.metadata.title,
        type: document.metadata.type,
        source: document.metadata.source,
        sourceUrl: document.metadata.sourceUrl,
        content: document.content,
        topics,
        status: 'active',
        createdBy: userId, // This will be mapped to created_by in the database layer
        fileSize: document.metadata.fileSize,
        fileName: document.metadata.fileName
      };

      const savedItem = await KnowledgeSourcesDB.create(knowledgeItem, userId, this.accessToken || undefined);

      // Validate the returned ID before proceeding with chunking
      if (!this.isValidUUID(savedItem.id)) {
        throw new Error(`Invalid UUID returned from database for knowledge source: ${savedItem.id}`);
      }

      console.log(`📝 Created knowledge source with valid ID: ${savedItem.id}, processing ${chunks.length} chunks`);

      // Generate embeddings for each chunk
      for (let i = 0; i < chunks.length; i++) {
        await this.generateAndStoreEmbeddings(savedItem.id, chunks[i], topics, i);
      }

      console.log(`✅ Added chunked document to Supabase: ${document.metadata.title} (${chunks.length} chunks, ID: ${savedItem.id})`);
    } catch (error) {
      console.error('Error adding chunked document to Supabase:', error);
      throw error;
    }
  }

  private async generateAndStoreEmbeddings(sourceId: string, content: string, topics: string[], chunkIndex: number = 0): Promise<void> {
    try {
      // Validate sourceId before processing
      if (!this.isValidUUID(sourceId)) {
        const error = new Error(`Invalid UUID format for sourceId during embedding generation: ${sourceId}`);
        console.error('❌ UUID Validation Error in generateAndStoreEmbeddings:', {
          sourceId,
          contentPreview: content?.substring(0, 50) + '...',
          topics: topics.slice(0, 3),
          chunkIndex,
          timestamp: new Date().toISOString(),
          stackTrace: error.stack
        });
        throw error;
      }

      let embedding: number[];

      if (this.useGeminiEmbeddings && this.geminiClient) {
        const textForEmbedding = `${content}\n\nTopics: ${topics.join(', ')}`;
        embedding = await this.geminiClient.generateEmbedding(textForEmbedding);
      } else {
        embedding = this.textToVector(content + ' ' + topics.join(' '));
      }

      await DocumentEmbeddingsDB.create(sourceId, content, embedding, chunkIndex);
      console.log(`✅ Generated and stored embedding for sourceId: ${sourceId}, chunk: ${chunkIndex}`);
    } catch (error) {
      console.error('Error generating and storing embeddings:', error);

      // Only attempt fallback if the error is not a UUID validation error
      if (error instanceof Error && !error.message.includes('Invalid UUID format')) {
        console.log('🔄 Attempting fallback with simple embedding...');
        try {
          // Store with simple embedding as fallback
          const embedding = this.textToVector(content + ' ' + topics.join(' '));
          await DocumentEmbeddingsDB.create(sourceId, content, embedding, chunkIndex);
          console.log(`✅ Fallback embedding stored for sourceId: ${sourceId}, chunk: ${chunkIndex}`);
        } catch (fallbackError) {
          console.error('❌ Fallback embedding also failed:', fallbackError);
          throw fallbackError;
        }
      } else {
        // Re-throw UUID validation errors without fallback
        throw error;
      }
    }
  }

  async removeDocument(id: string, userId: string): Promise<void> {
    try {
      console.log(`🗑️ Starting removal of document: ${id} for user: ${userId}`);

      // Validate inputs
      if (!this.isValidUUID(id)) {
        throw new Error(`Invalid UUID format for document ID: ${id}`);
      }
      if (!this.isValidUUID(userId)) {
        throw new Error(`Invalid UUID format for user ID: ${userId}`);
      }

      // Verify the document exists and belongs to the user before deletion
      const existingDocument = await KnowledgeSourcesDB.getById(id, userId);
      if (!existingDocument) {
        throw new Error(`Document ${id} not found or access denied for user ${userId}`);
      }

      console.log(`📄 Found document to delete: "${existingDocument.title}"`);

      // Delete embeddings first (this should always happen before deleting the source)
      console.log(`🧹 Deleting embeddings for document: ${id}`);
      await DocumentEmbeddingsDB.deleteBySourceId(id);

      // Delete knowledge source
      console.log(`🗑️ Deleting knowledge source: ${id}`);
      await KnowledgeSourcesDB.delete(id, userId);

      console.log(`✅ Successfully removed document from Supabase: ${id} ("${existingDocument.title}")`);
    } catch (error) {
      console.error('❌ Error removing document from Supabase:', error);
      throw error;
    }
  }

  // Helper function to validate UUIDs (accepts all UUID versions)
  private isValidUUID(uuid: string | undefined): boolean {
    return typeof uuid === 'string' && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(uuid);
  }

  // Data quality monitoring method
  async validateDataIntegrity(userId?: string): Promise<{
    totalEmbeddings: number;
    invalidSourceIds: number;
    orphanedEmbeddings: number;
    validEmbeddings: number;
    issues: string[];
  }> {
    try {
      console.log('🔍 Starting data integrity validation...');

      // Get all embeddings (this would need a new method in DocumentEmbeddingsDB)
      // For now, we'll use a search with a very low threshold to get many results
      const allEmbeddings = await DocumentEmbeddingsDB.searchSimilar(
        new Array(768).fill(0), // Zero vector to get diverse results
        1000, // Large limit
        -1 // Very low threshold to get all results
      );

      const invalidSourceIds = allEmbeddings.filter(e => !this.isValidUUID(e.sourceId));
      const validSourceIds = [...new Set(allEmbeddings.filter(e => this.isValidUUID(e.sourceId)).map(e => e.sourceId))];

      // Check for orphaned embeddings (valid UUIDs but no corresponding knowledge source)
      const orphanedEmbeddings = [];
      for (const sourceId of validSourceIds) {
        try {
          const source = await KnowledgeSourcesDB.getById(sourceId, userId);
          if (!source) {
            const orphanedCount = allEmbeddings.filter(e => e.sourceId === sourceId).length;
            orphanedEmbeddings.push({ sourceId, count: orphanedCount });
          }
        } catch (error) {
          const orphanedCount = allEmbeddings.filter(e => e.sourceId === sourceId).length;
          orphanedEmbeddings.push({ sourceId, count: orphanedCount });
        }
      }

      const issues = [];
      if (invalidSourceIds.length > 0) {
        issues.push(`${invalidSourceIds.length} embeddings with invalid UUID format`);
      }
      if (orphanedEmbeddings.length > 0) {
        const totalOrphaned = orphanedEmbeddings.reduce((sum, item) => sum + item.count, 0);
        issues.push(`${totalOrphaned} orphaned embeddings (${orphanedEmbeddings.length} unique sourceIds)`);
      }

      const result = {
        totalEmbeddings: allEmbeddings.length,
        invalidSourceIds: invalidSourceIds.length,
        orphanedEmbeddings: orphanedEmbeddings.reduce((sum, item) => sum + item.count, 0),
        validEmbeddings: allEmbeddings.length - invalidSourceIds.length - orphanedEmbeddings.reduce((sum, item) => sum + item.count, 0),
        issues
      };

      console.log('📊 Data integrity validation results:', result);

      if (invalidSourceIds.length > 0) {
        console.warn('⚠️ Invalid sourceId details:', {
          sample: invalidSourceIds.slice(0, 10).map(e => ({
            embeddingId: e.id,
            invalidSourceId: e.sourceId,
            chunkIndex: e.chunkIndex
          }))
        });
      }

      return result;
    } catch (error) {
      console.error('❌ Error during data integrity validation:', error);
      throw error;
    }
  }

  async search(query: string, limit: number = 5, threshold: number = 0.1): Promise<SearchResult[]> {
    try {
      // Generate query embedding
      let queryVector: number[];
      if (this.useGeminiEmbeddings && this.geminiClient) {
        queryVector = await this.geminiClient.generateQueryEmbedding(query);
      } else {
        queryVector = this.textToVector(query);
      }
      
      // Search similar embeddings
      const similarEmbeddings = await DocumentEmbeddingsDB.searchSimilar(queryVector, limit * 2, threshold);
      
      // Log and filter out embeddings with invalid sourceIds
      const embeddingsWithInvalidIds = similarEmbeddings.filter(e => !this.isValidUUID(e.sourceId));
      if (embeddingsWithInvalidIds.length > 0) {
        console.warn('⚠️ Found embeddings with invalid sourceIds during search:', {
          query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
          totalEmbeddings: similarEmbeddings.length,
          invalidCount: embeddingsWithInvalidIds.length,
          invalidPercentage: ((embeddingsWithInvalidIds.length / similarEmbeddings.length) * 100).toFixed(2) + '%',
          timestamp: new Date().toISOString(),
          sample: embeddingsWithInvalidIds.slice(0, 5).map(e => ({
            embeddingId: e.id,
            invalidSourceId: e.sourceId,
            contentPreview: e.content?.substring(0, 50) + '...',
            similarity: e.similarity?.toFixed(4),
            chunkIndex: e.chunkIndex
          })),
          searchThreshold: threshold,
          searchLimit: limit
        });

        // Log detailed information for debugging data quality issues
        console.warn('🔍 Detailed invalid UUID analysis:', {
          invalidSourceIds: [...new Set(embeddingsWithInvalidIds.map(e => e.sourceId))],
          affectedChunks: embeddingsWithInvalidIds.length,
          highestSimilarity: Math.max(...embeddingsWithInvalidIds.map(e => e.similarity || 0)).toFixed(4)
        });
      }

      // Filter out embeddings with invalid sourceIds and get unique sourceIds
      const validEmbeddings = similarEmbeddings.filter(e => this.isValidUUID(e.sourceId));
      const sourceIds = [...new Set(validEmbeddings.map(e => e.sourceId))];

      if (sourceIds.length === 0) {
        console.log('ℹ️ No valid source IDs found in search results', {
          originalEmbeddingsCount: similarEmbeddings.length,
          invalidEmbeddingsCount: embeddingsWithInvalidIds.length,
          query: query.substring(0, 50) + '...',
          threshold,
          limit
        });
        return [];
      }

      console.log(`🔍 Fetching ${sourceIds.length} knowledge sources from ${validEmbeddings.length} valid embeddings`);

      // Fetch knowledge sources with enhanced error handling
      const sources = await Promise.all(
        sourceIds.map(async id => {
          try {
            // Double-check UUID validity before database query
            if (!this.isValidUUID(id)) {
              console.error(`❌ Invalid UUID detected in sourceIds array: ${id}`);
              return null;
            }

            const source = await KnowledgeSourcesDB.getById(id);
            if (!source) {
              console.warn(`⚠️ Knowledge source not found for valid UUID: ${id}`);
            }
            return source;
          } catch (error) {
            console.error(`❌ Failed to fetch knowledge source ${id}:`, {
              error: error instanceof Error ? error.message : String(error),
              sourceId: id,
              isValidUUID: this.isValidUUID(id)
            });
            return null;
          }
        })
      );
      
      const validSources = sources.filter((s): s is KnowledgeItem => s !== null);
      const sourceMap = new Map(validSources.map(s => [s.id, s]));
      
      if (validSources.length < sources.length) {
        console.warn(`⚠️ Failed to fetch ${sources.length - validSources.length} knowledge sources`);
      }
      
      // Build search results
      const results: SearchResult[] = similarEmbeddings
        .map(embedding => {
          const source = sourceMap.get(embedding.sourceId);
          if (!source) return null;
          
          return {
            document: {
              id: source.id,
              content: embedding.content,
              metadata: {
                title: source.title,
                source: source.source,
                sourceUrl: source.sourceUrl,
                type: source.type,
                topics: source.topics,
                addedAt: source.addedAt.toISOString(),
                chunkIndex: embedding.chunkIndex
              }
            },
            score: embedding.similarity,
            matchType: 'vector' as const
          };
        })
        .filter(Boolean) as SearchResult[];
      
      return results.slice(0, limit);
    } catch (error) {
      console.error('Error searching Supabase vector store:', error);
      return [];
    }
  }

  async getAllDocuments(userId?: string): Promise<VectorDocument[]> {
    try {
      // If no userId provided and no instance userId, get all documents (public access)
      const targetUserId = userId ?? this.userId;
      const sources = await KnowledgeSourcesDB.getAll(targetUserId ?? undefined);

      return sources.map(source => ({
        id: source.id,
        content: source.content,
        metadata: {
          title: source.title,
          source: source.source,
          sourceUrl: source.sourceUrl,
          type: source.type,
          topics: source.topics,
          addedAt: source.addedAt.toISOString()
        }
      }));
    } catch (error) {
      console.error('Error getting all documents from Supabase:', error);
      return [];
    }
  }

  async getStats(userId?: string) {
    try {
      // If no userId provided and no instance userId, get stats for all documents (public access)
      const targetUserId = userId ?? this.userId;
      const documents = await this.getAllDocuments(targetUserId ?? undefined);
      const docTypes = documents.reduce((acc, doc) => {
        acc[doc.metadata.type] = (acc[doc.metadata.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const allTopics = [...new Set(documents.flatMap(doc => doc.metadata.topics))];

      return {
        totalDocuments: documents.length,
        documentTypes: docTypes,
        totalTopics: allTopics.length,
        topics: allTopics,
        sources: documents.map(doc => doc.metadata.title),
        embeddingType: this.useGeminiEmbeddings ? 'Gemini' : 'Simple'
      };
    } catch (error) {
      console.error('Error getting stats from Supabase:', error);
      return {
        totalDocuments: 0,
        documentTypes: {},
        totalTopics: 0,
        topics: [],
        sources: [],
        embeddingType: this.useGeminiEmbeddings ? 'Gemini' : 'Simple'
      };
    }
  }

  // Enhanced search with multiple strategies
  async searchWithKeywords(query: string, limit: number = 5, threshold: number = 0.8, userId?: string): Promise<SearchResult[]> {
    console.log(`🔍 Starting Supabase enhanced search for: "${query}"`);

    try {
      // Get all documents for keyword/topic search
      // If no userId provided and no instance userId, search all documents (public access)
      const targetUserId = userId ?? this.userId;
      const allDocuments = await this.getAllDocuments(targetUserId ?? undefined);
      console.log(`📊 Available documents: ${allDocuments.length}`);

      if (allDocuments.length === 0) {
        return [];
      }

      // Vector search
      const vectorResults = await this.search(query, limit * 2, threshold);
      console.log(`🧠 Vector search found: ${vectorResults.length} results`);

      // Topic-based search
      const topicResults = this.topicBasedSearch(query, allDocuments);
      console.log(`📋 Topic search found: ${topicResults.length} results`);

      // Keyword search
      const keywordResults = this.keywordSearch(query, allDocuments);
      console.log(`🔤 Keyword search found: ${keywordResults.length} results`);
      
      // Combine and deduplicate results
      const combinedResults = new Map<string, SearchResult>();
      
      // Add topic results with highest weight
      topicResults.forEach(result => {
        combinedResults.set(result.document.id, {
          ...result,
          score: result.score * 1.3,
          matchType: 'topic'
        });
      });
      
      // Add vector results
      vectorResults.forEach(result => {
        const existing = combinedResults.get(result.document.id);
        if (existing) {
          existing.score = Math.max(existing.score, result.score) + 0.15;
          existing.matchType = 'combined';
        } else {
          combinedResults.set(result.document.id, result);
        }
      });
      
      // Add keyword results
      keywordResults.forEach(result => {
        const existing = combinedResults.get(result.document.id);
        if (existing) {
          existing.score = Math.max(existing.score, result.score) + 0.1;
          existing.matchType = 'combined';
        } else {
          combinedResults.set(result.document.id, result);
        }
      });
      
      const finalResults = Array.from(combinedResults.values())
        .sort((a, b) => b.score - a.score)
        .slice(0, limit);
      
      console.log(`✅ Final Supabase search results: ${finalResults.length}`);
      finalResults.forEach((result, i) => {
        console.log(`  ${i + 1}. ${result.document.metadata.title} (${result.matchType}, score: ${result.score.toFixed(3)})`);
      });
      
      return finalResults;
    } catch (error) {
      console.error('Error in Supabase enhanced search:', error);
      return [];
    }
  }

  private topicBasedSearch(query: string, documents: VectorDocument[]): SearchResult[] {
    const queryLower = query.toLowerCase();
    const queryWords = queryLower.split(/\s+/).filter(word => word.length > 2);
    const results: SearchResult[] = [];

    for (const document of documents) {
      let topicScore = 0;
      
      for (const topic of document.metadata.topics) {
        const topicLower = topic.toLowerCase();
        
        if (queryWords.some(word => topicLower.includes(word) || word.includes(topicLower))) {
          topicScore += 3;
        }
        
        for (const word of queryWords) {
          if (word.length > 3 && topicLower.includes(word.substring(0, Math.min(word.length, 6)))) {
            topicScore += 1;
          }
        }
      }

      // Check title for topic relevance
      const titleLower = document.metadata.title.toLowerCase();
      for (const word of queryWords) {
        if (titleLower.includes(word)) {
          topicScore += 2;
        }
      }

      if (topicScore > 0) {
        results.push({
          document,
          score: Math.min(topicScore / 8, 1),
          matchType: 'topic'
        });
      }
    }

    return results.sort((a, b) => b.score - a.score);
  }

  private keywordSearch(query: string, documents: VectorDocument[]): SearchResult[] {
    const queryLower = query.toLowerCase();
    const queryWords = queryLower.split(/\s+/).filter(word => word.length > 2);
    const results: SearchResult[] = [];
    
    for (const document of documents) {
      const searchText = `${document.content} ${document.metadata.title} ${document.metadata.topics.join(' ')}`.toLowerCase();
      let score = 0;
      
      // Check for exact phrase matches
      if (searchText.includes(queryLower)) {
        score += 5;
      }
      
      // Individual word matching
      for (const word of queryWords) {
        const exactMatches = (searchText.match(new RegExp(`\\b${word}\\b`, 'g')) || []).length;
        score += exactMatches * 1;
      }
      
      if (score > 0) {
        results.push({
          document,
          score: score / 10,
          matchType: 'keyword'
        });
      }
    }
    
    return results.sort((a, b) => b.score - a.score);
  }
}