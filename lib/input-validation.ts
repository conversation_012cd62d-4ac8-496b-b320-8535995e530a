import { z } from 'zod';
import { SECURITY_CONFIG } from './security-config';

// Input validation schemas
export const schemas = {
  // Chat message validation
  chatMessage: z.object({
    message: z
      .string()
      .min(1, 'Message cannot be empty')
      .max(SECURITY_CONFIG.CONTENT.MAX_QUERY_LENGTH, `Message too long (max ${SECURITY_CONFIG.CONTENT.MAX_QUERY_LENGTH} characters)`)
      .refine(
        (msg) => !/<script|javascript:|data:|vbscript:/i.test(msg),
        'Message contains potentially dangerous content'
      ),
    sessionId: z.string().uuid().optional()
  }),

  // File upload validation
  fileUpload: z.object({
    file: z.custom<File>((file) => file instanceof File, 'Invalid file'),
    sourceUrl: z.string().url().optional()
  }).refine(
    (data) => {
      const file = data.file;
      return file.size <= SECURITY_CONFIG.FILE_UPLOAD.MAX_SIZE;
    },
    `File size must be less than ${SECURITY_CONFIG.FILE_UPLOAD.MAX_SIZE / 1024 / 1024}MB`
  ).refine(
    (data) => {
      const file = data.file;
      return SECURITY_CONFIG.FILE_UPLOAD.ALLOWED_TYPES.includes(file.type);
    },
    'Invalid file type. Only PDF files are allowed'
  ),

  // URL processing validation
  urlProcessing: z.object({
    url: z
      .string()
      .url('Invalid URL format')
      .refine(
        (url) => {
          try {
            const urlObj = new URL(url);
            return SECURITY_CONFIG.URL_PROCESSING.ALLOWED_PROTOCOLS.includes(urlObj.protocol);
          } catch {
            return false;
          }
        },
        'Only HTTP and HTTPS URLs are allowed'
      )
      .refine(
        (url) => {
          try {
            const urlObj = new URL(url);
            const hostname = urlObj.hostname.toLowerCase();
            
            // Check against blocked domains
            for (const blocked of SECURITY_CONFIG.URL_PROCESSING.BLOCKED_DOMAINS) {
              if (hostname.includes(blocked) || hostname === blocked) {
                return false;
              }
            }
            
            // Check for private IP ranges
            if (
              hostname.match(/^10\./) ||
              hostname.match(/^172\.(1[6-9]|2[0-9]|3[01])\./) ||
              hostname.match(/^192\.168\./) ||
              hostname === 'localhost' ||
              hostname === '127.0.0.1'
            ) {
              return false;
            }
            
            return true;
          } catch {
            return false;
          }
        },
        'URL points to a blocked or private network address'
      )
  }),

  // Admin operations validation
  adminOperation: z.object({
    action: z.enum(['validate', 'cleanup', 'export', 'import']),
    parameters: z.record(z.any()).optional()
  }),

  // User authentication validation
  userAuth: z.object({
    email: z.string().email('Invalid email format'),
    password: z
      .string()
      .min(8, 'Password must be at least 8 characters')
      .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
      .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
      .regex(/[0-9]/, 'Password must contain at least one number')
      .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character')
  })
};

// Validation middleware factory
export function createValidationMiddleware<T>(schema: z.ZodSchema<T>) {
  return (data: unknown): { success: true; data: T } | { success: false; error: string } => {
    try {
      const validatedData = schema.parse(data);
      return { success: true, data: validatedData };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errorMessage = error.errors.map(e => e.message).join(', ');
        return { success: false, error: errorMessage };
      }
      return { success: false, error: 'Validation failed' };
    }
  };
}

// Content filtering utilities
export class ContentFilter {
  private static readonly SUSPICIOUS_PATTERNS = [
    /\b(eval|exec|system|shell_exec|passthru)\s*\(/i,
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/gi,
    /data:text\/html/gi,
    /vbscript:/gi,
    /(union|select|insert|update|delete|drop|create|alter)\s+/gi,
    /\b(xss|csrf|injection|payload)\b/gi
  ];

  private static readonly PROFANITY_PATTERNS = [
    // Add profanity patterns as needed
    /\b(spam|scam|phishing)\b/gi
  ];

  static containsSuspiciousContent(content: string): boolean {
    return this.SUSPICIOUS_PATTERNS.some(pattern => pattern.test(content));
  }

  static containsProfanity(content: string): boolean {
    return this.PROFANITY_PATTERNS.some(pattern => pattern.test(content));
  }

  static sanitizeContent(content: string): string {
    return content
      .replace(/<script[^>]*>.*?<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/data:text\/html/gi, '')
      .replace(/vbscript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  }

  static isSpam(content: string): boolean {
    // Simple spam detection heuristics
    const repeatedChars = /(.)\1{10,}/g;
    const excessiveCaps = /[A-Z]{20,}/g;
    const excessiveUrls = (content.match(/https?:\/\/[^\s]+/g) || []).length > 5;
    
    return repeatedChars.test(content) || 
           excessiveCaps.test(content) || 
           excessiveUrls;
  }
}

// Rate limiting validation
export class RateLimitValidator {
  static validateRateLimit(
    current: number, 
    limit: number, 
    windowStart: number, 
    windowDuration: number
  ): { allowed: boolean; resetTime: number; remaining: number } {
    const now = Date.now();
    const windowEnd = windowStart + windowDuration;
    
    if (now > windowEnd) {
      // Window has expired, reset
      return {
        allowed: true,
        resetTime: now + windowDuration,
        remaining: limit - 1
      };
    }
    
    if (current >= limit) {
      // Rate limit exceeded
      return {
        allowed: false,
        resetTime: windowEnd,
        remaining: 0
      };
    }
    
    // Within rate limit
    return {
      allowed: true,
      resetTime: windowEnd,
      remaining: limit - current - 1
    };
  }
}

// File validation utilities
export class FileValidator {
  static readonly MAGIC_NUMBERS = {
    pdf: [0x25, 0x50, 0x44, 0x46], // %PDF
    jpg: [0xFF, 0xD8, 0xFF],
    png: [0x89, 0x50, 0x4E, 0x47],
    gif: [0x47, 0x49, 0x46, 0x38]
  };

  static async validateFileType(file: File): Promise<boolean> {
    if (file.type !== 'application/pdf') {
      return false;
    }

    // Check magic number
    const buffer = await file.slice(0, 4).arrayBuffer();
    const bytes = new Uint8Array(buffer);
    const pdfMagic = this.MAGIC_NUMBERS.pdf;
    
    return bytes.length >= pdfMagic.length && 
           pdfMagic.every((byte, index) => bytes[index] === byte);
  }

  static validateFileName(fileName: string): boolean {
    // Check for path traversal attempts
    if (fileName.includes('..') || fileName.includes('/') || fileName.includes('\\')) {
      return false;
    }

    // Check for suspicious extensions
    const suspiciousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.vbs', '.js'];
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    
    return !suspiciousExtensions.includes(extension);
  }
}
