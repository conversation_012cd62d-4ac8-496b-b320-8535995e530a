interface OpenRouterResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

export class OpenRouterClient {
  private apiKey: string;
  private baseUrl = 'https://openrouter.ai/api/v1';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  private async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private isRetryableError(error: any): boolean {
    // Check for network errors
    if (error.name === 'TypeError' && error.message.includes('fetch failed')) {
      return true;
    }
    
    // Check for socket errors
    if (error.cause && error.cause.name === 'SocketError') {
      return true;
    }
    
    return false;
  }

  private isRetryableStatus(status: number): boolean {
    // Retry on rate limiting and server errors
    return status === 429 || (status >= 500 && status < 600);
  }

  async generateResponse(
    prompt: string,
    context: string,
    model: string = 'deepseek/deepseek-chat-v3-0324:free'
  ): Promise<string> {
    const maxRetries = 3;
    const baseDelay = 1000; // 1 second

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const systemPrompt = `You are a helpful AI assistant that answers questions based ONLY on the provided context. 
        If the context doesn't contain enough information to answer the question, say so clearly.
        Always be accurate and cite the source when possible.
        
        Context: ${context}`;

        const response = await fetch(`${this.baseUrl}/chat/completions`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
            'X-Title': 'AI Knowledge Assistant'
          },
          body: JSON.stringify({
            model,
            messages: [
              { role: 'system', content: systemPrompt },
              { role: 'user', content: prompt }
            ],
            temperature: 0.3,
            max_tokens: 1000
          })
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          
          // Check if this is a retryable HTTP status
          if (this.isRetryableStatus(response.status) && attempt < maxRetries) {
            const delay = baseDelay * Math.pow(2, attempt);
            console.warn(`OpenRouter API returned ${response.status}, retrying in ${delay}ms (attempt ${attempt + 1}/${maxRetries + 1})`);
            await this.sleep(delay);
            continue;
          }
          
          throw new Error(`OpenRouter API error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
        }

        const data: OpenRouterResponse = await response.json();
        return data.choices[0]?.message?.content || 'I apologize, but I could not generate a response.';
        
      } catch (error) {
        console.error(`OpenRouter API error (attempt ${attempt + 1}/${maxRetries + 1}):`, error);
        
        // Check if this is a retryable error and we have attempts left
        if (this.isRetryableError(error) && attempt < maxRetries) {
          const delay = baseDelay * Math.pow(2, attempt);
          console.warn(`Retrying OpenRouter API call in ${delay}ms due to network error`);
          await this.sleep(delay);
          continue;
        }
        
        // If we've exhausted retries or it's not a retryable error, throw
        throw error;
      }
    }

    // This should never be reached, but TypeScript requires it
    throw new Error('Maximum retry attempts exceeded');
  }
}