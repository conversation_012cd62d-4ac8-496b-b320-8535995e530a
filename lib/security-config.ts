// Security configuration and utilities
export const SECURITY_CONFIG = {
  // Rate limiting thresholds
  RATE_LIMITS: {
    PUBLIC_CHAT: {
      requests: 20,
      window: '5m',
      description: 'Public chat queries'
    },
    ADMIN_OPERATIONS: {
      requests: 5,
      window: '10m',
      description: 'Admin panel operations'
    },
    FILE_UPLOADS: {
      requests: 2,
      window: '1h',
      description: 'PDF file uploads'
    },
    URL_PROCESSING: {
      requests: 3,
      window: '30m',
      description: 'URL content processing'
    },
    GENERAL_API: {
      requests: 30,
      window: '1m',
      description: 'General API requests'
    }
  },

  // File upload restrictions
  FILE_UPLOAD: {
    MAX_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_TYPES: ['application/pdf'],
    MAX_FILES_PER_USER: 50,
    SCAN_FOR_MALWARE: true
  },

  // URL processing restrictions
  URL_PROCESSING: {
    TIMEOUT: 10000, // 10 seconds
    MAX_CONTENT_SIZE: 5 * 1024 * 1024, // 5MB
    ALLOWED_PROTOCOLS: ['http:', 'https:'],
    BLOCKED_DOMAINS: [
      'localhost',
      '127.0.0.1',
      '0.0.0.0',
      '::1',
      '***************', // AWS metadata
      '10.0.0.0/8',
      '**********/12',
      '***********/16'
    ],
    USER_AGENT: 'KnowledgeBaseBot/1.0'
  },

  // Authentication and authorization
  AUTH: {
    SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
    MAX_LOGIN_ATTEMPTS: 5,
    LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes
    REQUIRE_EMAIL_VERIFICATION: true,
    ADMIN_EMAIL_DOMAINS: [] // Add allowed admin domains
  },

  // Content security
  CONTENT: {
    MAX_QUERY_LENGTH: 1000,
    MAX_RESPONSE_LENGTH: 10000,
    PROFANITY_FILTER: true,
    SPAM_DETECTION: true
  },

  // Monitoring and logging
  MONITORING: {
    LOG_FAILED_REQUESTS: true,
    LOG_RATE_LIMIT_VIOLATIONS: true,
    LOG_SECURITY_EVENTS: true,
    ALERT_THRESHOLD: 100, // alerts after 100 violations per hour
    METRICS_RETENTION: 30 * 24 * 60 * 60 * 1000 // 30 days
  }
};

// Security utility functions
export class SecurityUtils {
  // Validate and sanitize user input
  static sanitizeInput(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential XSS characters
      .substring(0, SECURITY_CONFIG.CONTENT.MAX_QUERY_LENGTH);
  }

  // Check if URL is safe to process
  static isUrlSafe(url: string): boolean {
    try {
      const urlObj = new URL(url);
      
      // Check protocol
      if (!SECURITY_CONFIG.URL_PROCESSING.ALLOWED_PROTOCOLS.includes(urlObj.protocol)) {
        return false;
      }

      // Check for blocked domains/IPs
      const hostname = urlObj.hostname.toLowerCase();
      for (const blocked of SECURITY_CONFIG.URL_PROCESSING.BLOCKED_DOMAINS) {
        if (hostname.includes(blocked) || hostname === blocked) {
          return false;
        }
      }

      return true;
    } catch {
      return false;
    }
  }

  // Generate secure session ID
  static generateSecureId(): string {
    return crypto.randomUUID();
  }

  // Hash sensitive data
  static async hashData(data: string): Promise<string> {
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  // Validate file upload
  static validateFileUpload(file: File): { valid: boolean; error?: string } {
    if (file.size > SECURITY_CONFIG.FILE_UPLOAD.MAX_SIZE) {
      return {
        valid: false,
        error: `File size exceeds ${SECURITY_CONFIG.FILE_UPLOAD.MAX_SIZE / 1024 / 1024}MB limit`
      };
    }

    if (!SECURITY_CONFIG.FILE_UPLOAD.ALLOWED_TYPES.includes(file.type)) {
      return {
        valid: false,
        error: `File type ${file.type} not allowed`
      };
    }

    return { valid: true };
  }

  // Log security event
  static logSecurityEvent(event: {
    type: 'rate_limit' | 'auth_failure' | 'suspicious_activity' | 'error';
    ip: string;
    userAgent?: string;
    endpoint?: string;
    details?: any;
  }) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      ...event
    };

    // In production, send to monitoring service
    if (process.env.NODE_ENV === 'production') {
      // TODO: Integrate with monitoring service (e.g., Sentry, DataDog)
      console.warn('SECURITY_EVENT:', JSON.stringify(logEntry));
    } else {
      console.warn('SECURITY_EVENT:', logEntry);
    }
  }
}

// Rate limiting key generators
export class RateLimitKeys {
  static forIP(ip: string, type: string): string {
    return `rate_limit:${type}:ip:${ip}`;
  }

  static forUser(userId: string, type: string): string {
    return `rate_limit:${type}:user:${userId}`;
  }

  static forEndpoint(endpoint: string, ip: string): string {
    return `rate_limit:endpoint:${endpoint}:${ip}`;
  }
}

// Security headers configuration
export const SECURITY_HEADERS = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://api.openrouter.ai https://*.supabase.co;"
};
