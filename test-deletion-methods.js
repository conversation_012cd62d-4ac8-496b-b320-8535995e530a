// Test the deletion methods directly
// This simulates what happens when the API is called

const testSourceId = '0da6c819-f57b-4b2b-921c-374ef76bff08';
const userId = '8e8e914b-f5e5-4294-8148-11e19029e05d';

console.log('Testing deletion methods directly...');
console.log(`Source ID: ${testSourceId}`);
console.log(`User ID: ${userId}`);

// We'll test this by making a direct call to the API endpoint
// using curl instead of Node.js to avoid connection issues

console.log('\nTo test the deletion, run this curl command:');
console.log(`curl -X DELETE "http://localhost:3000/api/knowledge-sources/${testSourceId}" \\`);
console.log(`  -H "Authorization: Bearer test-token" \\`);
console.log(`  -H "Content-Type: application/json" \\`);
console.log(`  -v`);

console.log('\nThis will test our enhanced deletion functionality with detailed logging.');
console.log('Check the server terminal for detailed logs about the deletion process.');
