-- Migration to add search history support to chat_messages table
-- This adds the missing columns needed for search history functionality

-- Add user_id column to support anonymous and authenticated users
ALTER TABLE chat_messages 
ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;

-- Add metadata column to store search query metadata
ALTER TABLE chat_messages 
ADD COLUMN IF NOT EXISTS metadata JSONB;

-- Create index on metadata for efficient search query filtering
CREATE INDEX IF NOT EXISTS idx_chat_messages_metadata_type 
ON chat_messages USING GIN ((metadata->>'type'));

-- Create index on user_id for efficient user-specific queries
CREATE INDEX IF NOT EXISTS idx_chat_messages_user_id 
ON chat_messages (user_id);

-- Create composite index for search history queries
CREATE INDEX IF NOT EXISTS idx_chat_messages_search_history 
ON chat_messages (user_id, created_at DESC) 
WHERE metadata->>'type' = 'search_query';

-- Update RLS policies to support anonymous users and search queries
-- Drop existing policy and recreate with broader access
DROP POLICY IF EXISTS "Users can manage their own messages" ON chat_messages;

-- New policy that allows:
-- 1. Authenticated users to access their own messages
-- 2. Anonymous users to access messages with their session_id
-- 3. Public read access for search queries (for popular searches)
CREATE POLICY "Enhanced message access policy"
  ON chat_messages FOR ALL
  TO authenticated, anon
  USING (
    -- Authenticated users can access their own messages
    (auth.uid() IS NOT NULL AND user_id = auth.uid()) OR
    -- Authenticated users can access messages from their sessions
    (auth.uid() IS NOT NULL AND session_id IN (
      SELECT id FROM chat_sessions WHERE user_id = auth.uid()
    )) OR
    -- Anonymous users can access messages with their session_id (for search history)
    (auth.uid() IS NULL AND session_id IS NOT NULL) OR
    -- Public read access for search queries (for popular searches functionality)
    (metadata->>'type' = 'search_query' AND TG_OP = 'SELECT')
  );

-- Allow anonymous users to insert search queries
CREATE POLICY "Allow anonymous search query insertion"
  ON chat_messages FOR INSERT
  TO anon
  WITH CHECK (
    metadata->>'type' = 'search_query' AND
    role = 'user' AND
    session_id IS NOT NULL AND
    user_id IS NULL
  );

-- Allow authenticated users to insert search queries
CREATE POLICY "Allow authenticated search query insertion"
  ON chat_messages FOR INSERT
  TO authenticated
  WITH CHECK (
    metadata->>'type' = 'search_query' AND
    role = 'user' AND
    (user_id = auth.uid() OR user_id IS NULL)
  );
