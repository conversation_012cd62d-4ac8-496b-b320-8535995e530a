// Test script to verify knowledge source deletion functionality
// Run with: node test-deletion.js

const fetch = require('node-fetch');

async function testDeletion() {
  const baseUrl = 'http://localhost:3000';
  const testSourceId = '4017ad03-4cf2-4646-b5e2-bbc3156afabf'; // Test with one of the existing sources
  
  // You would need to get a real auth token from the browser
  // For now, this is just a structure to show how to test
  const authToken = 'YOUR_AUTH_TOKEN_HERE';
  
  try {
    console.log(`Testing deletion of knowledge source: ${testSourceId}`);
    
    const response = await fetch(`${baseUrl}/api/knowledge-sources/${testSourceId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    
    console.log('Response status:', response.status);
    console.log('Response body:', result);
    
    if (response.ok) {
      console.log('✅ Deletion API call succeeded');
    } else {
      console.log('❌ Deletion API call failed');
    }
    
  } catch (error) {
    console.error('Error testing deletion:', error);
  }
}

// Uncomment to run the test (after adding a real auth token)
// testDeletion();

console.log('Test script ready. Add a real auth token and uncomment the testDeletion() call to run.');
