#!/usr/bin/env node

/**
 * Security Testing Script
 * Tests the implemented security measures
 */

const https = require('https');
const http = require('http');

const BASE_URL = process.env.TEST_URL || 'http://localhost:3000';
const TEST_RESULTS = [];

// Test configuration
const TESTS = {
  RATE_LIMITING: true,
  INPUT_VALIDATION: true,
  SECURITY_HEADERS: true,
  AUTHENTICATION: true,
  FILE_UPLOAD: true
};

// Utility function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const protocol = options.protocol === 'https:' ? https : http;
    
    const req = protocol.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: body
        });
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(data);
    }
    
    req.end();
  });
}

// Test rate limiting
async function testRateLimiting() {
  console.log('\n🔄 Testing Rate Limiting...');
  
  const url = new URL('/api/chat-public', BASE_URL);
  const options = {
    hostname: url.hostname,
    port: url.port || (url.protocol === 'https:' ? 443 : 80),
    path: url.pathname,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  };

  const testData = JSON.stringify({ message: 'test message' });
  let rateLimitHit = false;
  let requestCount = 0;

  try {
    // Make multiple requests to trigger rate limiting
    for (let i = 0; i < 15; i++) {
      const response = await makeRequest(options, testData);
      requestCount++;
      
      console.log(`Request ${i + 1}: Status ${response.statusCode}`);
      
      // Check for rate limit headers
      if (response.headers['x-ratelimit-limit']) {
        console.log(`  Rate Limit: ${response.headers['x-ratelimit-remaining']}/${response.headers['x-ratelimit-limit']}`);
      }
      
      if (response.statusCode === 429) {
        rateLimitHit = true;
        console.log('  ✅ Rate limit triggered successfully');
        break;
      }
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    TEST_RESULTS.push({
      test: 'Rate Limiting',
      passed: rateLimitHit,
      details: `Rate limit ${rateLimitHit ? 'triggered' : 'not triggered'} after ${requestCount} requests`
    });

  } catch (error) {
    TEST_RESULTS.push({
      test: 'Rate Limiting',
      passed: false,
      details: `Error: ${error.message}`
    });
  }
}

// Test input validation
async function testInputValidation() {
  console.log('\n🔄 Testing Input Validation...');
  
  const url = new URL('/api/chat-public', BASE_URL);
  const options = {
    hostname: url.hostname,
    port: url.port || (url.protocol === 'https:' ? 443 : 80),
    path: url.pathname,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  };

  const maliciousPayloads = [
    '<script>alert("xss")</script>',
    'javascript:alert("xss")',
    '"><script>alert("xss")</script>',
    'SELECT * FROM users',
    'UNION SELECT password FROM users',
    'A'.repeat(2000), // Very long string
    '', // Empty string
    null,
    undefined
  ];

  let validationWorking = true;

  try {
    for (const payload of maliciousPayloads) {
      const testData = JSON.stringify({ message: payload });
      const response = await makeRequest(options, testData);
      
      console.log(`Testing payload: ${typeof payload === 'string' ? payload.substring(0, 50) + '...' : payload}`);
      console.log(`  Status: ${response.statusCode}`);
      
      // Should reject malicious payloads
      if (response.statusCode === 200 && (
        typeof payload === 'string' && (
          payload.includes('<script>') ||
          payload.includes('javascript:') ||
          payload.includes('SELECT') ||
          payload.length > 1000
        )
      )) {
        validationWorking = false;
        console.log('  ❌ Malicious payload accepted');
      } else if (response.statusCode === 400) {
        console.log('  ✅ Malicious payload rejected');
      }
    }

    TEST_RESULTS.push({
      test: 'Input Validation',
      passed: validationWorking,
      details: validationWorking ? 'All malicious payloads properly rejected' : 'Some malicious payloads were accepted'
    });

  } catch (error) {
    TEST_RESULTS.push({
      test: 'Input Validation',
      passed: false,
      details: `Error: ${error.message}`
    });
  }
}

// Test security headers
async function testSecurityHeaders() {
  console.log('\n🔄 Testing Security Headers...');
  
  const url = new URL('/', BASE_URL);
  const options = {
    hostname: url.hostname,
    port: url.port || (url.protocol === 'https:' ? 443 : 80),
    path: url.pathname,
    method: 'GET'
  };

  const requiredHeaders = [
    'x-content-type-options',
    'x-frame-options',
    'x-xss-protection',
    'referrer-policy'
  ];

  try {
    const response = await makeRequest(options);
    let headersPresent = 0;

    console.log('Checking security headers:');
    for (const header of requiredHeaders) {
      if (response.headers[header]) {
        console.log(`  ✅ ${header}: ${response.headers[header]}`);
        headersPresent++;
      } else {
        console.log(`  ❌ ${header}: Missing`);
      }
    }

    const allHeadersPresent = headersPresent === requiredHeaders.length;

    TEST_RESULTS.push({
      test: 'Security Headers',
      passed: allHeadersPresent,
      details: `${headersPresent}/${requiredHeaders.length} security headers present`
    });

  } catch (error) {
    TEST_RESULTS.push({
      test: 'Security Headers',
      passed: false,
      details: `Error: ${error.message}`
    });
  }
}

// Test authentication bypass attempts
async function testAuthentication() {
  console.log('\n🔄 Testing Authentication...');
  
  const url = new URL('/api/admin/data-quality', BASE_URL);
  const options = {
    hostname: url.hostname,
    port: url.port || (url.protocol === 'https:' ? 443 : 80),
    path: url.pathname,
    method: 'GET'
  };

  try {
    // Test without authentication
    const response1 = await makeRequest(options);
    console.log(`No auth: Status ${response1.statusCode}`);
    
    // Test with invalid token
    const options2 = { ...options, headers: { 'Authorization': 'Bearer invalid_token' } };
    const response2 = await makeRequest(options2);
    console.log(`Invalid token: Status ${response2.statusCode}`);

    const authWorking = response1.statusCode === 401 && response2.statusCode === 401;

    TEST_RESULTS.push({
      test: 'Authentication',
      passed: authWorking,
      details: authWorking ? 'Unauthorized requests properly rejected' : 'Authentication bypass possible'
    });

  } catch (error) {
    TEST_RESULTS.push({
      test: 'Authentication',
      passed: false,
      details: `Error: ${error.message}`
    });
  }
}

// Main test runner
async function runSecurityTests() {
  console.log('🔒 Starting Security Tests');
  console.log(`Testing URL: ${BASE_URL}`);
  console.log('=' * 50);

  if (TESTS.RATE_LIMITING) await testRateLimiting();
  if (TESTS.INPUT_VALIDATION) await testInputValidation();
  if (TESTS.SECURITY_HEADERS) await testSecurityHeaders();
  if (TESTS.AUTHENTICATION) await testAuthentication();

  // Print results
  console.log('\n📊 Test Results:');
  console.log('=' * 50);
  
  let passedTests = 0;
  for (const result of TEST_RESULTS) {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.test}: ${result.details}`);
    if (result.passed) passedTests++;
  }

  console.log('\n📈 Summary:');
  console.log(`${passedTests}/${TEST_RESULTS.length} tests passed`);
  
  if (passedTests === TEST_RESULTS.length) {
    console.log('🎉 All security tests passed!');
    process.exit(0);
  } else {
    console.log('⚠️  Some security tests failed. Review the results above.');
    process.exit(1);
  }
}

// Run tests if called directly
if (require.main === module) {
  runSecurityTests().catch(console.error);
}

module.exports = { runSecurityTests };
